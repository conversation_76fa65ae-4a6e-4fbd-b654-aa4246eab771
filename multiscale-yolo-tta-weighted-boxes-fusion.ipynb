{"cells": [{"cell_type": "code", "execution_count": 1, "id": "38af6af4", "metadata": {"_cell_guid": "b1076dfc-b9ad-4769-8c92-a6c4dae69d19", "_uuid": "8f2839f25d086af736a60e9eeb907d3b93b6e0e5", "execution": {"iopub.execute_input": "2025-07-08T16:07:55.080850Z", "iopub.status.busy": "2025-07-08T16:07:55.080585Z", "iopub.status.idle": "2025-07-08T16:09:10.033484Z", "shell.execute_reply": "2025-07-08T16:09:10.032517Z"}, "papermill": {"duration": 74.958875, "end_time": "2025-07-08T16:09:10.035512", "exception": false, "start_time": "2025-07-08T16:07:55.076637", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting ultralytics\r\n", "  Downloading ultralytics-8.3.163-py3-none-any.whl.metadata (37 kB)\r\n", "Requirement already satisfied: numpy>=1.23.0 in /usr/local/lib/python3.11/dist-packages (from ultralytics) (1.26.4)\r\n", "Requirement already satisfied: matplotlib>=3.3.0 in /usr/local/lib/python3.11/dist-packages (from ultralytics) (3.7.2)\r\n", "Requirement already satisfied: opencv-python>=4.6.0 in /usr/local/lib/python3.11/dist-packages (from ultralytics) (*********)\r\n", "Requirement already satisfied: pillow>=7.1.2 in /usr/local/lib/python3.11/dist-packages (from ultralytics) (11.1.0)\r\n", "Requirement already satisfied: pyyaml>=5.3.1 in /usr/local/lib/python3.11/dist-packages (from ultralytics) (6.0.2)\r\n", "Requirement already satisfied: requests>=2.23.0 in /usr/local/lib/python3.11/dist-packages (from ultralytics) (2.32.3)\r\n", "Requirement already satisfied: scipy>=1.4.1 in /usr/local/lib/python3.11/dist-packages (from ultralytics) (1.15.2)\r\n", "Requirement already satisfied: torch>=1.8.0 in /usr/local/lib/python3.11/dist-packages (from ultralytics) (2.6.0+cu124)\r\n", "Requirement already satisfied: torchvision>=0.9.0 in /usr/local/lib/python3.11/dist-packages (from ultralytics) (0.21.0+cu124)\r\n", "Requirement already satisfied: tqdm>=4.64.0 in /usr/local/lib/python3.11/dist-packages (from ultralytics) (4.67.1)\r\n", "Requirement already satisfied: psutil in /usr/local/lib/python3.11/dist-packages (from ultralytics) (7.0.0)\r\n", "Requirement already satisfied: py-cpuinfo in /usr/local/lib/python3.11/dist-packages (from ultralytics) (9.0.0)\r\n", "Requirement already satisfied: pandas>=1.1.4 in /usr/local/lib/python3.11/dist-packages (from ultralytics) (2.2.3)\r\n", "Collecting ultralytics-thop>=2.0.0 (from ultralytics)\r\n", "  Downloading ultralytics_thop-2.0.14-py3-none-any.whl.metadata (9.4 kB)\r\n", "Requirement already satisfied: contourpy>=1.0.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.3.0->ultralytics) (1.3.1)\r\n", "Requirement already satisfied: cycler>=0.10 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.3.0->ultralytics) (0.12.1)\r\n", "Requirement already satisfied: fonttools>=4.22.0 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.3.0->ultralytics) (4.57.0)\r\n", "Requirement already satisfied: kiwisolver>=1.0.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.3.0->ultralytics) (1.4.8)\r\n", "Requirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.3.0->ultralytics) (25.0)\r\n", "Requirement already satisfied: pyparsing<3.1,>=2.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.3.0->ultralytics) (3.0.9)\r\n", "Requirement already satisfied: python-dateutil>=2.7 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.3.0->ultralytics) (2.9.0.post0)\r\n", "Requirement already satisfied: mkl_fft in /usr/local/lib/python3.11/dist-packages (from numpy>=1.23.0->ultralytics) (1.3.8)\r\n", "Requirement already satisfied: mkl_random in /usr/local/lib/python3.11/dist-packages (from numpy>=1.23.0->ultralytics) (1.2.4)\r\n", "Requirement already satisfied: mkl_umath in /usr/local/lib/python3.11/dist-packages (from numpy>=1.23.0->ultralytics) (0.1.1)\r\n", "Requirement already satisfied: mkl in /usr/local/lib/python3.11/dist-packages (from numpy>=1.23.0->ultralytics) (2025.1.0)\r\n", "Requirement already satisfied: tbb4py in /usr/local/lib/python3.11/dist-packages (from numpy>=1.23.0->ultralytics) (2022.1.0)\r\n", "Requirement already satisfied: mkl-service in /usr/local/lib/python3.11/dist-packages (from numpy>=1.23.0->ultralytics) (2.4.1)\r\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.11/dist-packages (from pandas>=1.1.4->ultralytics) (2025.2)\r\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.11/dist-packages (from pandas>=1.1.4->ultralytics) (2025.2)\r\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests>=2.23.0->ultralytics) (3.4.2)\r\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests>=2.23.0->ultralytics) (3.10)\r\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests>=2.23.0->ultralytics) (2.4.0)\r\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.11/dist-packages (from requests>=2.23.0->ultralytics) (2025.4.26)\r\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from torch>=1.8.0->ultralytics) (3.18.0)\r\n", "Requirement already satisfied: typing-extensions>=4.10.0 in /usr/local/lib/python3.11/dist-packages (from torch>=1.8.0->ultralytics) (4.13.2)\r\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.11/dist-packages (from torch>=1.8.0->ultralytics) (3.4.2)\r\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.11/dist-packages (from torch>=1.8.0->ultralytics) (3.1.6)\r\n", "Requirement already satisfied: fsspec in /usr/local/lib/python3.11/dist-packages (from torch>=1.8.0->ultralytics) (2025.3.2)\r\n", "Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=1.8.0->ultralytics) (12.4.127)\r\n", "Requirement already satisfied: nvidia-cuda-runtime-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=1.8.0->ultralytics) (12.4.127)\r\n", "Requirement already satisfied: nvidia-cuda-cupti-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=1.8.0->ultralytics) (12.4.127)\r\n", "Collecting nvidia-cudnn-cu12==******** (from torch>=1.8.0->ultralytics)\r\n", "  Downloading nvidia_cudnn_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\r\n", "Collecting nvidia-cublas-cu12==******** (from torch>=1.8.0->ultralytics)\r\n", "  Downloading nvidia_cublas_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\r\n", "Collecting nvidia-cufft-cu12==******** (from torch>=1.8.0->ultralytics)\r\n", "  Downloading nvidia_cufft_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\r\n", "Collecting nvidia-curand-cu12==********** (from torch>=1.8.0->ultralytics)\r\n", "  Downloading nvidia_curand_cu12-**********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\r\n", "Collecting nvidia-cusolver-cu12==******** (from torch>=1.8.0->ultralytics)\r\n", "  Downloading nvidia_cusolver_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\r\n", "Collecting nvidia-cusparse-cu12==********** (from torch>=1.8.0->ultralytics)\r\n", "  Downloading nvidia_cusparse_cu12-**********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\r\n", "Requirement already satisfied: nvidia-cusparselt-cu12==0.6.2 in /usr/local/lib/python3.11/dist-packages (from torch>=1.8.0->ultralytics) (0.6.2)\r\n", "Requirement already satisfied: nvidia-nccl-cu12==2.21.5 in /usr/local/lib/python3.11/dist-packages (from torch>=1.8.0->ultralytics) (2.21.5)\r\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=1.8.0->ultralytics) (12.4.127)\r\n", "Collecting nvidia-nvjitlink-cu12==12.4.127 (from torch>=1.8.0->ultralytics)\r\n", "  Downloading nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\r\n", "Requirement already satisfied: triton==3.2.0 in /usr/local/lib/python3.11/dist-packages (from torch>=1.8.0->ultralytics) (3.2.0)\r\n", "Requirement already satisfied: sympy==1.13.1 in /usr/local/lib/python3.11/dist-packages (from torch>=1.8.0->ultralytics) (1.13.1)\r\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from sympy==1.13.1->torch>=1.8.0->ultralytics) (1.3.0)\r\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.11/dist-packages (from python-dateutil>=2.7->matplotlib>=3.3.0->ultralytics) (1.17.0)\r\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.11/dist-packages (from jinja2->torch>=1.8.0->ultralytics) (3.0.2)\r\n", "Requirement already satisfied: intel-openmp<2026,>=2024 in /usr/local/lib/python3.11/dist-packages (from mkl->numpy>=1.23.0->ultralytics) (2024.2.0)\r\n", "Requirement already satisfied: tbb==2022.* in /usr/local/lib/python3.11/dist-packages (from mkl->numpy>=1.23.0->ultralytics) (2022.1.0)\r\n", "Requirement already satisfied: tcmlib==1.* in /usr/local/lib/python3.11/dist-packages (from tbb==2022.*->mkl->numpy>=1.23.0->ultralytics) (1.3.0)\r\n", "Requirement already satisfied: intel-cmplr-lib-rt in /usr/local/lib/python3.11/dist-packages (from mkl_umath->numpy>=1.23.0->ultralytics) (2024.2.0)\r\n", "Requirement already satisfied: intel-cmplr-lib-ur==2024.2.0 in /usr/local/lib/python3.11/dist-packages (from intel-openmp<2026,>=2024->mkl->numpy>=1.23.0->ultralytics) (2024.2.0)\r\n", "Downloading ultralytics-8.3.163-py3-none-any.whl (1.0 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.0/1.0 MB\u001b[0m \u001b[31m29.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading nvidia_cublas_cu12-********-py3-none-manylinux2014_x86_64.whl (363.4 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m363.4/363.4 MB\u001b[0m \u001b[31m4.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading nvidia_cudnn_cu12-********-py3-none-manylinux2014_x86_64.whl (664.8 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m664.8/664.8 MB\u001b[0m \u001b[31m2.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading nvidia_cufft_cu12-********-py3-none-manylinux2014_x86_64.whl (211.5 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m211.5/211.5 MB\u001b[0m \u001b[31m2.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading nvidia_curand_cu12-**********-py3-none-manylinux2014_x86_64.whl (56.3 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m56.3/56.3 MB\u001b[0m \u001b[31m29.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading nvidia_cusolver_cu12-********-py3-none-manylinux2014_x86_64.whl (127.9 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m127.9/127.9 MB\u001b[0m \u001b[31m13.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading nvidia_cusparse_cu12-**********-py3-none-manylinux2014_x86_64.whl (207.5 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m207.5/207.5 MB\u001b[0m \u001b[31m8.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (21.1 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m21.1/21.1 MB\u001b[0m \u001b[31m80.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading ultralytics_thop-2.0.14-py3-none-any.whl (26 kB)\r\n", "Installing collected packages: nvidia-nvjitlink-cu12, nvidia-curand-cu12, nvidia-cufft-cu12, nvidia-cublas-cu12, nvidia-cusparse-cu12, nvidia-cudnn-cu12, nvidia-cusolver-cu12, ultralytics-thop, ultralytics\r\n", "  Attempting uninstall: nvidia-nvjitlink-cu12\r\n", "    Found existing installation: nvidia-nvjitlink-cu12 12.9.41\r\n", "    Uninstalling nvidia-nvjitlink-cu12-12.9.41:\r\n", "      Successfully uninstalled nvidia-nvjitlink-cu12-12.9.41\r\n", "  Attempting uninstall: nvidia-curand-cu12\r\n", "    Found existing installation: nvidia-curand-cu12 10.3.10.19\r\n", "    Uninstalling nvidia-curand-cu12-10.3.10.19:\r\n", "      Successfully uninstalled nvidia-curand-cu12-10.3.10.19\r\n", "  Attempting uninstall: nvidia-cufft-cu12\r\n", "    Found existing installation: nvidia-cufft-cu12 11.4.0.6\r\n", "    Uninstalling nvidia-cufft-cu12-11.4.0.6:\r\n", "      Successfully uninstalled nvidia-cufft-cu12-11.4.0.6\r\n", "  Attempting uninstall: nvidia-cublas-cu12\r\n", "    Found existing installation: nvidia-cublas-cu12 12.9.0.13\r\n", "    Uninstalling nvidia-cublas-cu12-12.9.0.13:\r\n", "      Successfully uninstalled nvidia-cublas-cu12-12.9.0.13\r\n", "  Attempting uninstall: nvidia-cusparse-cu12\r\n", "    Found existing installation: nvidia-cusparse-cu12 12.5.9.5\r\n", "    Uninstalling nvidia-cusparse-cu12-12.5.9.5:\r\n", "      Successfully uninstalled nvidia-cusparse-cu12-12.5.9.5\r\n", "  Attempting uninstall: nvidia-cudnn-cu12\r\n", "    Found existing installation: nvidia-cudnn-cu12 9.3.0.75\r\n", "    Uninstalling nvidia-cudnn-cu12-9.3.0.75:\r\n", "      Successfully uninstalled nvidia-cudnn-cu12-9.3.0.75\r\n", "  Attempting uninstall: nvidia-cusolver-cu12\r\n", "    Found existing installation: nvidia-cusolver-cu12 *********\r\n", "    Uninstalling nvidia-cusolver-cu12-*********:\r\n", "      Successfully uninstalled nvidia-cusolver-cu12-*********\r\n", "Successfully installed nvidia-cublas-cu12-******** nvidia-cudnn-cu12-******** nvidia-cufft-cu12-******** nvidia-curand-cu12-********** nvidia-cusolver-cu12-******** nvidia-cusparse-cu12-********** nvidia-nvjitlink-cu12-12.4.127 ultralytics-8.3.163 ultralytics-thop-2.0.14\r\n"]}], "source": ["!pip install ultralytics"]}, {"cell_type": "code", "execution_count": 2, "id": "697ed6e8", "metadata": {"execution": {"iopub.execute_input": "2025-07-08T16:09:10.129107Z", "iopub.status.busy": "2025-07-08T16:09:10.128034Z", "iopub.status.idle": "2025-07-08T16:09:16.347714Z", "shell.execute_reply": "2025-07-08T16:09:16.346931Z"}, "papermill": {"duration": 6.26833, "end_time": "2025-07-08T16:09:16.349224", "exception": false, "start_time": "2025-07-08T16:09:10.080894", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting ensemble-boxes\r\n", "  Downloading ensemble_boxes-1.0.9-py3-none-any.whl.metadata (728 bytes)\r\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.11/dist-packages (from ensemble-boxes) (1.26.4)\r\n", "Requirement already satisfied: pandas in /usr/local/lib/python3.11/dist-packages (from ensemble-boxes) (2.2.3)\r\n", "Requirement already satisfied: numba in /usr/local/lib/python3.11/dist-packages (from ensemble-boxes) (0.60.0)\r\n", "Requirement already satisfied: llvmlite<0.44,>=0.43.0dev0 in /usr/local/lib/python3.11/dist-packages (from numba->ensemble-boxes) (0.43.0)\r\n", "Requirement already satisfied: mkl_fft in /usr/local/lib/python3.11/dist-packages (from numpy->ensemble-boxes) (1.3.8)\r\n", "Requirement already satisfied: mkl_random in /usr/local/lib/python3.11/dist-packages (from numpy->ensemble-boxes) (1.2.4)\r\n", "Requirement already satisfied: mkl_umath in /usr/local/lib/python3.11/dist-packages (from numpy->ensemble-boxes) (0.1.1)\r\n", "Requirement already satisfied: mkl in /usr/local/lib/python3.11/dist-packages (from numpy->ensemble-boxes) (2025.1.0)\r\n", "Requirement already satisfied: tbb4py in /usr/local/lib/python3.11/dist-packages (from numpy->ensemble-boxes) (2022.1.0)\r\n", "Requirement already satisfied: mkl-service in /usr/local/lib/python3.11/dist-packages (from numpy->ensemble-boxes) (2.4.1)\r\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.11/dist-packages (from pandas->ensemble-boxes) (2.9.0.post0)\r\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.11/dist-packages (from pandas->ensemble-boxes) (2025.2)\r\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.11/dist-packages (from pandas->ensemble-boxes) (2025.2)\r\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.11/dist-packages (from python-dateutil>=2.8.2->pandas->ensemble-boxes) (1.17.0)\r\n", "Requirement already satisfied: intel-openmp<2026,>=2024 in /usr/local/lib/python3.11/dist-packages (from mkl->numpy->ensemble-boxes) (2024.2.0)\r\n", "Requirement already satisfied: tbb==2022.* in /usr/local/lib/python3.11/dist-packages (from mkl->numpy->ensemble-boxes) (2022.1.0)\r\n", "Requirement already satisfied: tcmlib==1.* in /usr/local/lib/python3.11/dist-packages (from tbb==2022.*->mkl->numpy->ensemble-boxes) (1.3.0)\r\n", "Requirement already satisfied: intel-cmplr-lib-rt in /usr/local/lib/python3.11/dist-packages (from mkl_umath->numpy->ensemble-boxes) (2024.2.0)\r\n", "Requirement already satisfied: intel-cmplr-lib-ur==2024.2.0 in /usr/local/lib/python3.11/dist-packages (from intel-openmp<2026,>=2024->mkl->numpy->ensemble-boxes) (2024.2.0)\r\n", "Downloading ensemble_boxes-1.0.9-py3-none-any.whl (23 kB)\r\n", "Installing collected packages: ensemble-boxes\r\n", "Successfully installed ensemble-boxes-1.0.9\r\n"]}], "source": ["!pip install ensemble-boxes"]}, {"cell_type": "code", "execution_count": 3, "id": "cac17659", "metadata": {"execution": {"iopub.execute_input": "2025-07-08T16:09:16.399413Z", "iopub.status.busy": "2025-07-08T16:09:16.399121Z", "iopub.status.idle": "2025-07-08T16:09:16.427789Z", "shell.execute_reply": "2025-07-08T16:09:16.427080Z"}, "papermill": {"duration": 0.055067, "end_time": "2025-07-08T16:09:16.428886", "exception": false, "start_time": "2025-07-08T16:09:16.373819", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'train': ['clutter/train/images', 'couch_far_10/train/images', 'far_10_half_clutter/train/images', 'film_grain_10_half_clutter/train/images', 'large_plant_10/train/images', 'no_clutter_10/train/images', 'table_close_10/train/images'], 'val': ['clutter/val/images', 'couch_far_10/val/images', 'far_10_half_clutter/val/images', 'film_grain_10_half_clutter/val/images', 'large_plant_10/val/images', 'no_clutter_10/val/images', 'table_close_10/val/images'], 'test': 'TestImages', 'nc': 1, 'names': ['Soup']}\n"]}], "source": ["# prompt: open yolo_params.yaml file\n", "\n", "import yaml\n", "\n", "file_paths=\"/kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/yolo_params.yaml\"\n", "try:\n", "  with open(\"/kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/yolo_params.yaml\", 'r') as file:\n", "    yolo_params = yaml.safe_load(file)\n", "    print(yolo_params)\n", "except FileNotFoundError:\n", "  print(f\"Error: The file '{file_path}' was not found.\")\n", "except yaml.YAMLError as e:\n", "  print(f\"Error parsing YAML file: {e}\")"]}, {"cell_type": "code", "execution_count": 4, "id": "564a46c1", "metadata": {"execution": {"iopub.execute_input": "2025-07-08T16:09:16.479082Z", "iopub.status.busy": "2025-07-08T16:09:16.478796Z", "iopub.status.idle": "2025-07-08T16:09:16.495090Z", "shell.execute_reply": "2025-07-08T16:09:16.494187Z"}, "papermill": {"duration": 0.042552, "end_time": "2025-07-08T16:09:16.496291", "exception": false, "start_time": "2025-07-08T16:09:16.453739", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["yolo_params.yaml updated successfully in /kaggle/working/\n", "Updated yolo_params.yaml:\n", "names:\n", "- Soup\n", "nc: 1\n", "test: /kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/TestImages\n", "train:\n", "- /kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/clutter/train\n", "- /kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/couch_far_10/train/images\n", "- /kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/far_10_half_clutter/train/images\n", "- /kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/film_grain_10_half_clutter/train/images\n", "- /kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/large_plant_10/train/images\n", "- /kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/no_clutter_10/train/images\n", "- /kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/table_close_10/train/images\n", "- /kaggle/input/multi-instance/output (3)/home/<USER>/Output/2025-07-08-12-56-01/train/images\n", "- /kaggle/input/multi-instance/output (4)/home/<USER>/Output/2025-07-08-12-57-55/train/images\n", "- /kaggle/input/multi-instance/output (5)/home/<USER>/Output/2025-07-08-12-58-30/train/images\n", "- /kaggle/input/multi-instance/output (6)/home/<USER>/Output/2025-07-08-12-59-30/train/images\n", "- /kaggle/input/multi-instance/output (7)/home/<USER>/Output/2025-07-08-12-59-33/train/images\n", "- /kaggle/input/multi-instance/output (8)/home/<USER>/Output/2025-07-08-13-31-50/train/images\n", "val:\n", "- /kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/clutter/val/images\n", "- /kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/couch_far_10/val/images\n", "- /kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/far_10_half_clutter/val/images\n", "- /kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/film_grain_10_half_clutter/val/images\n", "- /kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/large_plant_10/val/images\n", "- /kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/no_clutter_10/val/images\n", "- /kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/table_close_10/val/images\n", "- /kaggle/input/multi-instance/output (3)/home/<USER>/Output/2025-07-08-12-56-01/val/images\n", "- /kaggle/input/multi-instance/output (4)/home/<USER>/Output/2025-07-08-12-57-55/val/images\n", "- /kaggle/input/multi-instance/output (5)/home/<USER>/Output/2025-07-08-12-58-30/val/images\n", "- /kaggle/input/multi-instance/output (6)/home/<USER>/Output/2025-07-08-12-59-30/val/images\n", "- /kaggle/input/multi-instance/output (7)/home/<USER>/Output/2025-07-08-12-59-33/val/images\n", "- /kaggle/input/multi-instance/output (8)/home/<USER>/Output/2025-07-08-13-31-50/val/images\n", "\n"]}], "source": ["import yaml\n", "import shutil\n", "\n", "# First, copy the original file to a writable location\n", "shutil.copy(\"/kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/yolo_params.yaml\", \n", "            \"/kaggle/working/yolo_params.yaml\")\n", "\n", "# Read the existing YAML file from the writable location\n", "with open(\"/kaggle/working/yolo_params.yaml\", 'r') as file:\n", "    yolo_params = yaml.safe_load(file)\n", "\n", "# Modify the YAML content\n", "yolo_params['train'] = [\n", "    '/kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/clutter/train', \n", "    '/kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/couch_far_10/train/images', \n", "    '/kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/far_10_half_clutter/train/images', \n", "    '/kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/film_grain_10_half_clutter/train/images', \n", "    '/kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/large_plant_10/train/images', \n", "    '/kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/no_clutter_10/train/images', \n", "    '/kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/table_close_10/train/images',\n", "    '/kaggle/input/multi-instance/output (3)/home/<USER>/Output/2025-07-08-12-56-01/train/images',\n", "    '/kaggle/input/multi-instance/output (4)/home/<USER>/Output/2025-07-08-12-57-55/train/images',\n", "    '/kaggle/input/multi-instance/output (5)/home/<USER>/Output/2025-07-08-12-58-30/train/images',\n", "    '/kaggle/input/multi-instance/output (6)/home/<USER>/Output/2025-07-08-12-59-30/train/images',\n", "    '/kaggle/input/multi-instance/output (7)/home/<USER>/Output/2025-07-08-12-59-33/train/images',\n", "    '/kaggle/input/multi-instance/output (8)/home/<USER>/Output/2025-07-08-13-31-50/train/images',\n", "    \n", "]\n", "\n", "yolo_params['val'] = [\n", "    '/kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/clutter/val/images', \n", "    '/kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/couch_far_10/val/images', \n", "    '/kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/far_10_half_clutter/val/images', \n", "    '/kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/film_grain_10_half_clutter/val/images', \n", "    '/kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/large_plant_10/val/images', \n", "    '/kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/no_clutter_10/val/images', \n", "    '/kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/table_close_10/val/images',\n", "    '/kaggle/input/multi-instance/output (3)/home/<USER>/Output/2025-07-08-12-56-01/val/images',\n", "    '/kaggle/input/multi-instance/output (4)/home/<USER>/Output/2025-07-08-12-57-55/val/images',\n", "    '/kaggle/input/multi-instance/output (5)/home/<USER>/Output/2025-07-08-12-58-30/val/images',\n", "    '/kaggle/input/multi-instance/output (6)/home/<USER>/Output/2025-07-08-12-59-30/val/images',\n", "    '/kaggle/input/multi-instance/output (7)/home/<USER>/Output/2025-07-08-12-59-33/val/images',\n", "    '/kaggle/input/multi-instance/output (8)/home/<USER>/Output/2025-07-08-13-31-50/val/images',\n", "]\n", "\n", "yolo_params['test'] = '/kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/TestImages'\n", "yolo_params['nc'] = 1\n", "yolo_params['names'] = ['Soup']\n", "\n", "# Write the modified content back to the file in the writable location\n", "with open(\"/kaggle/working/yolo_params.yaml\", 'w') as file:\n", "    yaml.dump(yolo_params, file, default_flow_style=False)\n", "\n", "print(\"yolo_params.yaml updated successfully in /kaggle/working/\")\n", "\n", "# Optional: Read the file again to verify the changes\n", "with open(\"/kaggle/working/yolo_params.yaml\", 'r') as file:\n", "    updated_yolo_params = yaml.safe_load(file)\n", "    print(\"Updated yolo_params.yaml:\")\n", "    print(yaml.dump(updated_yolo_params, default_flow_style=False))"]}, {"cell_type": "code", "execution_count": 5, "id": "48693d9b", "metadata": {"execution": {"iopub.execute_input": "2025-07-08T16:09:16.546662Z", "iopub.status.busy": "2025-07-08T16:09:16.545944Z", "iopub.status.idle": "2025-07-08T16:09:21.332257Z", "shell.execute_reply": "2025-07-08T16:09:21.331470Z"}, "papermill": {"duration": 4.813203, "end_time": "2025-07-08T16:09:21.333453", "exception": false, "start_time": "2025-07-08T16:09:16.520250", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating new Ultralytics Settings v0.0.6 file ✅ \n", "View Ultralytics Settings with 'yolo settings' or at '/root/.config/Ultralytics/settings.json'\n", "Update Settings with 'yolo settings key=value', i.e. 'yolo settings runs_dir=path/to/dir'. For help see https://docs.ultralytics.com/quickstart/#ultralytics-settings.\n"]}, {"data": {"text/plain": ["<torch._C.Generator at 0x781812948f70>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from ultralytics import YOLO\n", "from pathlib import Path\n", "import csv\n", "import os\n", "import random\n", "import torch\n", "# Set random seeds for reproducibility\n", "np.random.seed(42)\n", "random.seed(42)\n", "torch.manual_seed(42)"]}, {"cell_type": "code", "execution_count": 6, "id": "8f8797a4", "metadata": {"execution": {"iopub.execute_input": "2025-07-08T16:09:21.382650Z", "iopub.status.busy": "2025-07-08T16:09:21.382296Z", "iopub.status.idle": "2025-07-08T18:20:38.496915Z", "shell.execute_reply": "2025-07-08T18:20:38.496154Z"}, "papermill": {"duration": 7877.14284, "end_time": "2025-07-08T18:20:38.500435", "exception": false, "start_time": "2025-07-08T16:09:21.357595", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11m.pt to 'yolo11m.pt'...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 38.8M/38.8M [00:00<00:00, 268MB/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Ultralytics 8.3.163 🚀 Python-3.11.11 torch-2.6.0+cu124 CUDA:0 (Tesla P100-PCIE-16GB, 16269MiB)\n", "\u001b[34m\u001b[1mengine/trainer: \u001b[0magnostic_nms=False, amp=True, augment=False, auto_augment=randaugment, batch=4, bgr=0.0, box=7.5, cache=False, cfg=None, classes=None, close_mosaic=20, cls=0.5, conf=None, copy_paste=0.0, copy_paste_mode=flip, cos_lr=True, cutmix=0.0, data=/kaggle/working/yolo_params.yaml, degrees=0.0, deterministic=True, device=None, dfl=1.5, dnn=False, dropout=0.0, dynamic=False, embed=None, epochs=50, erasing=0.4, exist_ok=False, fliplr=0.5, flipud=0, format=torchscript, fraction=1.0, freeze=None, half=False, hsv_h=0.015, hsv_s=0.7, hsv_v=0.4, imgsz=1440, int8=False, iou=0.7, keras=False, kobj=1.0, line_width=None, lr0=0.001, lrf=0.01, mask_ratio=4, max_det=300, mixup=0.0, mode=train, model=yolo11m.pt, momentum=0.937, mosaic=1.0, multi_scale=False, name=train, nbs=64, nms=False, opset=None, optimize=False, optimizer=SGD, overlap_mask=True, patience=300, perspective=0.0, plots=True, pose=12.0, pretrained=True, profile=False, project=None, rect=False, resume=False, retina_masks=False, save=True, save_conf=False, save_crop=False, save_dir=runs/detect/train, save_frames=False, save_json=False, save_period=2, save_txt=False, scale=0.5, seed=0, shear=0, show=False, show_boxes=True, show_conf=True, show_labels=True, simplify=True, single_cls=False, source=None, split=val, stream_buffer=False, task=detect, time=None, tracker=botsort.yaml, translate=0.1, val=True, verbose=True, vid_stride=1, visualize=False, warmup_bias_lr=0.1, warmup_epochs=3, warmup_momentum=1, weight_decay=0.0005, workers=8, workspace=None\n", "Downloading https://ultralytics.com/assets/Arial.ttf to '/root/.config/Ultralytics/Arial.ttf'...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 755k/755k [00:00<00:00, 26.3MB/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Overriding model.yaml nc=80 with nc=1\n", "\n", "                   from  n    params  module                                       arguments                     \n", "  0                  -1  1      1856  ultralytics.nn.modules.conv.Conv             [3, 64, 3, 2]                 \n", "  1                  -1  1     73984  ultralytics.nn.modules.conv.Conv             [64, 128, 3, 2]               \n", "  2                  -1  1    111872  ultralytics.nn.modules.block.C3k2            [128, 256, 1, True, 0.25]     \n", "  3                  -1  1    590336  ultralytics.nn.modules.conv.Conv             [256, 256, 3, 2]              \n", "  4                  -1  1    444928  ultralytics.nn.modules.block.C3k2            [256, 512, 1, True, 0.25]     \n", "  5                  -1  1   2360320  ultralytics.nn.modules.conv.Conv             [512, 512, 3, 2]              \n", "  6                  -1  1   1380352  ultralytics.nn.modules.block.C3k2            [512, 512, 1, True]           \n", "  7                  -1  1   2360320  ultralytics.nn.modules.conv.Conv             [512, 512, 3, 2]              \n", "  8                  -1  1   1380352  ultralytics.nn.modules.block.C3k2            [512, 512, 1, True]           \n", "  9                  -1  1    656896  ultralytics.nn.modules.block.SPPF            [512, 512, 5]                 \n", " 10                  -1  1    990976  ultralytics.nn.modules.block.C2PSA           [512, 512, 1]                 \n", " 11                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']          \n", " 12             [-1, 6]  1         0  ultralytics.nn.modules.conv.Concat           [1]                           \n", " 13                  -1  1   1642496  ultralytics.nn.modules.block.C3k2            [1024, 512, 1, True]          \n", " 14                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']          \n", " 15             [-1, 4]  1         0  ultralytics.nn.modules.conv.Concat           [1]                           \n", " 16                  -1  1    542720  ultralytics.nn.modules.block.C3k2            [1024, 256, 1, True]          \n", " 17                  -1  1    590336  ultralytics.nn.modules.conv.Conv             [256, 256, 3, 2]              \n", " 18            [-1, 13]  1         0  ultralytics.nn.modules.conv.Concat           [1]                           \n", " 19                  -1  1   1511424  ultralytics.nn.modules.block.C3k2            [768, 512, 1, True]           \n", " 20                  -1  1   2360320  ultralytics.nn.modules.conv.Conv             [512, 512, 3, 2]              \n", " 21            [-1, 10]  1         0  ultralytics.nn.modules.conv.Concat           [1]                           \n", " 22                  -1  1   1642496  ultralytics.nn.modules.block.C3k2            [1024, 512, 1, True]          \n", " 23        [16, 19, 22]  1   1411795  ultralytics.nn.modules.head.Detect           [1, [256, 512, 512]]          \n", "YOLO11m summary: 231 layers, 20,053,779 parameters, 20,053,763 gradients, 68.2 GFLOPs\n", "\n", "Transferred 643/649 items from pretrained weights\n", "Freezing layer 'model.23.dfl.conv.weight'\n", "\u001b[34m\u001b[1mAMP: \u001b[0mrunning Automatic Mixed Precision (AMP) checks...\n", "Downloading https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11n.pt to 'yolo11n.pt'...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 5.35M/5.35M [00:00<00:00, 109MB/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[34m\u001b[1mAMP: \u001b[0mchecks passed ✅\n", "\u001b[34m\u001b[1mtrain: \u001b[0mFast image access ✅ (ping: 0.0±0.0 ms, read: 127.4±74.4 MB/s, size: 3096.2 KB)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[34m\u001b[1mtrain: \u001b[0mScanning /kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/clutter/train/labels... 597 images, 7 backgrounds, 0 corrupt: 100%|██████████| 597/597 [00:07<00:00, 85.05it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING ⚠️ \u001b[34m\u001b[1mtrain: \u001b[0mCache directory /kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/clutter/train is not writeable, cache not saved.\n", "\u001b[34m\u001b[1malbumentations: \u001b[0mB<PERSON>r(p=0.01, blur_limit=(3, 7)), MedianBlur(p=0.01, blur_limit=(3, 7)), ToGray(p=0.01, num_output_channels=3, method='weighted_average'), CLAHE(p=0.01, clip_limit=(1.0, 4.0), tile_grid_size=(8, 8))\n", "\u001b[34m\u001b[1mval: \u001b[0mFast image access ✅ (ping: 0.0±0.0 ms, read: 155.0±17.5 MB/s, size: 3431.0 KB)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[34m\u001b[1mval: \u001b[0mScanning /kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/clutter/val/labels... 73 images, 3 backgrounds, 0 corrupt: 100%|██████████| 73/73 [00:00<00:00, 73.90it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING ⚠️ \u001b[34m\u001b[1mval: \u001b[0mCache directory /kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/clutter/val is not writeable, cache not saved.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Plotting labels to runs/detect/train/labels.jpg... \n", "\u001b[34m\u001b[1moptimizer:\u001b[0m SGD(lr=0.001, momentum=0.937) with parameter groups 106 weight(decay=0.0), 113 weight(decay=0.0005), 112 bias(decay=0.0)\n", "Image sizes 1440 train, 1440 val\n", "Using 4 dataloader workers\n", "Logging results to \u001b[1mruns/detect/train\u001b[0m\n", "Starting training for 50 epochs...\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["       1/50      11.1G     0.3433       1.57     0.8922          0       1440: 100%|██████████| 150/150 [02:34<00:00,  1.03s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.52it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.876      0.902      0.931      0.913\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["       2/50      10.8G     0.3184     0.4797     0.8758          5       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.42it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.908      0.925      0.958      0.942\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["       3/50      10.9G     0.2961     0.3734     0.8546          3       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.42it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173       0.98      0.908      0.973      0.958\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["       4/50      10.9G     0.2986     0.3393     0.8539          2       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.38it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.966      0.925      0.978      0.958\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["       5/50      10.8G     0.2659     0.2825     0.8331         12       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.44it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.965      0.944       0.98      0.969\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["       6/50      10.8G     0.2718     0.2772     0.8382          4       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.41it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.943      0.958      0.981      0.965\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["       7/50      10.8G     0.2698     0.2706     0.8335          7       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.40it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173       0.98      0.942       0.98      0.964\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["       8/50      10.8G     0.2545     0.2431     0.8312         11       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.38it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.975      0.954      0.982      0.962\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["       9/50      10.8G     0.2516     0.2463     0.8235          0       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.42it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.977       0.96      0.983      0.972\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      10/50      10.8G     0.2555     0.2392     0.8214          8       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.38it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.964      0.965      0.985      0.971\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      11/50      10.8G       0.25     0.2313     0.8308          3       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.39it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.971      0.969      0.987      0.971\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      12/50      10.7G     0.2496     0.2218     0.8331         10       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  3.31it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.982       0.97      0.987      0.969\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      13/50      10.8G     0.2545     0.2268     0.8359          5       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.40it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173       0.97      0.971      0.987      0.973\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      14/50      10.8G     0.2463      0.225     0.8271          5       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.41it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.988      0.975      0.988      0.969\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      15/50      10.8G     0.2551     0.2247     0.8337          1       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.41it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.982      0.971      0.987      0.968\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      16/50      10.8G     0.2426      0.219     0.8211          3       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.38it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.998      0.977      0.988      0.976\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      17/50      10.8G     0.2371     0.2025     0.8256         12       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.39it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.998      0.965      0.988      0.976\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      18/50      10.8G     0.2368     0.2016     0.8277          9       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.42it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.995       0.96      0.988      0.975\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      19/50      10.8G     0.2385     0.2037     0.8287          7       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.43it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.976      0.977      0.989      0.976\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      20/50      10.8G     0.2284      0.196     0.8286          8       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.43it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.977       0.98      0.988      0.978\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      21/50      10.8G     0.2307     0.1961     0.8276          2       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.40it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.992      0.977      0.988      0.977\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      22/50      10.7G     0.2374     0.2017     0.8279         12       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.40it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.993      0.965      0.988      0.974\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      23/50      10.8G     0.2269      0.186     0.8172         10       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  3.27it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.994      0.977      0.987      0.976\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      24/50      10.8G     0.2238     0.1894     0.8188         10       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.33it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173       0.99      0.977      0.986      0.975\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      25/50      10.8G      0.206     0.1739     0.8114          7       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.38it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.987      0.977      0.987      0.974\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      26/50      10.8G     0.2196     0.1829     0.8224          5       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.42it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.993      0.965      0.988      0.974\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      27/50      10.8G     0.2098     0.1804     0.8178          7       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.39it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.992      0.954      0.988      0.974\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      28/50      10.8G     0.2072     0.1723       0.81          8       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.40it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.996      0.965      0.986      0.975\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      29/50      10.8G     0.2098      0.171     0.8176          3       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.42it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.998      0.965      0.988      0.975\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      30/50      10.7G     0.2075     0.1686     0.8165          3       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.42it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.988      0.977      0.989      0.978\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Closing dataloader mosaic\n", "\u001b[34m\u001b[1malbumentations: \u001b[0mB<PERSON>r(p=0.01, blur_limit=(3, 7)), MedianBlur(p=0.01, blur_limit=(3, 7)), ToGray(p=0.01, num_output_channels=3, method='weighted_average'), CLAHE(p=0.01, clip_limit=(1.0, 4.0), tile_grid_size=(8, 8))\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      31/50      10.8G     0.1718     0.1571     0.7801          3       1440: 100%|██████████| 150/150 [02:33<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.41it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173          1      0.953      0.988      0.979\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      32/50      10.8G     0.1697      0.149     0.7756          1       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.43it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.993      0.977      0.989       0.98\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      33/50      10.8G     0.1722     0.1516      0.778          3       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.43it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.993      0.983      0.989       0.98\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      34/50      10.8G      0.174     0.1585     0.7764          3       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.42it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.988      0.971      0.987       0.98\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      35/50      10.8G     0.1655     0.1432     0.7774          3       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.39it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.987      0.983      0.988      0.979\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      36/50      10.8G     0.1617     0.1454     0.7701          3       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.43it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.998      0.954      0.988      0.982\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      37/50      10.8G       0.16      0.143     0.7776          1       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.44it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173       0.99       0.96      0.989      0.982\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      38/50      10.7G     0.1553     0.1361     0.7695          3       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.40it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.993      0.971      0.988       0.98\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      39/50      10.8G     0.1546     0.1398     0.7698          2       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.44it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.999      0.971      0.988      0.979\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      40/50      10.8G     0.1504     0.1304     0.7751          3       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.44it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.994      0.976      0.988       0.98\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      41/50      10.8G     0.1547     0.1372     0.7747          3       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.37it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173          1      0.971      0.988      0.981\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      42/50      10.8G     0.1511     0.1294      0.769          3       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.43it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173          1      0.971      0.988      0.981\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      43/50      10.8G     0.1488     0.1266     0.7788          3       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.44it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.994      0.977      0.988      0.981\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      44/50      10.8G     0.1572     0.1303     0.7747          3       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.34it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173          1      0.971      0.988      0.982\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      45/50      10.8G     0.1475     0.1264      0.778          3       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.37it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173          1      0.975      0.988      0.983\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      46/50      10.7G     0.1428     0.1252     0.7673          2       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.42it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173          1      0.974       0.99      0.983\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      47/50      10.8G     0.1492     0.1298     0.7648          3       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.44it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173          1      0.975       0.99      0.983\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      48/50      10.8G     0.1459     0.1265     0.7713          2       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.46it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173          1      0.976       0.99      0.982\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      49/50      10.8G     0.1444     0.1243     0.7656          2       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.39it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173      0.999      0.977       0.99      0.982\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      50/50      10.8G     0.1493     0.1264     0.7757          3       1440: 100%|██████████| 150/150 [02:32<00:00,  1.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:02<00:00,  3.39it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173          1      0.977       0.99      0.982\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "50 epochs completed in 2.178 hours.\n", "Optimizer stripped from runs/detect/train/weights/last.pt, 40.6MB\n", "Optimizer stripped from runs/detect/train/weights/best.pt, 40.6MB\n", "\n", "Validating runs/detect/train/weights/best.pt...\n", "Ultralytics 8.3.163 🚀 Python-3.11.11 torch-2.6.0+cu124 CUDA:0 (Tesla P100-PCIE-16GB, 16269MiB)\n", "YOLO11m summary (fused): 125 layers, 20,030,803 parameters, 0 gradients, 67.6 GFLOPs\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.91it/s]\n", "/usr/local/lib/python3.11/dist-packages/matplotlib/colors.py:721: RuntimeWarning: invalid value encountered in less\n", "  xa[xa < 0] = -1\n", "/usr/local/lib/python3.11/dist-packages/matplotlib/colors.py:721: RuntimeWarning: invalid value encountered in less\n", "  xa[xa < 0] = -1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all         73        173          1      0.975       0.99      0.983\n", "Speed: 0.6ms preprocess, 34.7ms inference, 0.0ms loss, 3.1ms postprocess per image\n", "Results saved to \u001b[1mruns/detect/train\u001b[0m\n"]}, {"data": {"text/plain": ["ultralytics.utils.metrics.DetMetrics object with attributes:\n", "\n", "ap_class_index: array([0])\n", "box: ultralytics.utils.metrics.Metric object\n", "confusion_matrix: <ultralytics.utils.metrics.ConfusionMatrix object at 0x7816948a3690>\n", "curves: ['Precision-Recall(B)', 'F1-Confidence(B)', 'Precision-Confidence(B)', 'Recall-Confidence(B)']\n", "curves_results: [[array([          0,    0.001001,    0.002002,    0.003003,    0.004004,    0.005005,    0.006006,    0.007007,    0.008008,    0.009009,     0.01001,    0.011011,    0.012012,    0.013013,    0.014014,    0.015015,    0.016016,    0.017017,    0.018018,    0.019019,     0.02002,    0.021021,    0.022022,    0.023023,\n", "          0.024024,    0.025025,    0.026026,    0.027027,    0.028028,    0.029029,     0.03003,    0.031031,    0.032032,    0.033033,    0.034034,    0.035035,    0.036036,    0.037037,    0.038038,    0.039039,     0.04004,    0.041041,    0.042042,    0.043043,    0.044044,    0.045045,    0.046046,    0.047047,\n", "          0.048048,    0.049049,     0.05005,    0.051051,    0.052052,    0.053053,    0.054054,    0.055055,    0.056056,    0.057057,    0.058058,    0.059059,     0.06006,    0.061061,    0.062062,    0.063063,    0.064064,    0.065065,    0.066066,    0.067067,    0.068068,    0.069069,     0.07007,    0.071071,\n", "          0.072072,    0.073073,    0.074074,    0.075075,    0.076076,    0.077077,    0.078078,    0.079079,     0.08008,    0.081081,    0.082082,    0.083083,    0.084084,    0.085085,    0.086086,    0.087087,    0.088088,    0.089089,     0.09009,    0.091091,    0.092092,    0.093093,    0.094094,    0.095095,\n", "          0.096096,    0.097097,    0.098098,    0.099099,      0.1001,      0.1011,      0.1021,      0.1031,      0.1041,     0.10511,     0.10611,     0.10711,     0.10811,     0.10911,     0.11011,     0.11111,     0.11211,     0.11311,     0.11411,     0.11512,     0.11612,     0.11712,     0.11812,     0.11912,\n", "           0.12012,     0.12112,     0.12212,     0.12312,     0.12412,     0.12513,     0.12613,     0.12713,     0.12813,     0.12913,     0.13013,     0.13113,     0.13213,     0.13313,     0.13413,     0.13514,     0.13614,     0.13714,     0.13814,     0.13914,     0.14014,     0.14114,     0.14214,     0.14314,\n", "           0.14414,     0.14515,     0.14615,     0.14715,     0.14815,     0.14915,     0.15015,     0.15115,     0.15215,     0.15315,     0.15415,     0.15516,     0.15616,     0.15716,     0.15816,     0.15916,     0.16016,     0.16116,     0.16216,     0.16316,     0.16416,     0.16517,     0.16617,     0.16717,\n", "           0.16817,     0.16917,     0.17017,     0.17117,     0.17217,     0.17317,     0.17417,     0.17518,     0.17618,     0.17718,     0.17818,     0.17918,     0.18018,     0.18118,     0.18218,     0.18318,     0.18418,     0.18519,     0.18619,     0.18719,     0.18819,     0.18919,     0.19019,     0.19119,\n", "           0.19219,     0.19319,     0.19419,      0.1952,      0.1962,      0.1972,      0.1982,      0.1992,      0.2002,      0.2012,      0.2022,      0.2032,      0.2042,     0.20521,     0.20621,     0.20721,     0.20821,     0.20921,     0.21021,     0.21121,     0.21221,     0.21321,     0.21421,     0.21522,\n", "           0.21622,     0.21722,     0.21822,     0.21922,     0.22022,     0.22122,     0.22222,     0.22322,     0.22422,     0.22523,     0.22623,     0.22723,     0.22823,     0.22923,     0.23023,     0.23123,     0.23223,     0.23323,     0.23423,     0.23524,     0.23624,     0.23724,     0.23824,     0.23924,\n", "           0.24024,     0.24124,     0.24224,     0.24324,     0.24424,     0.24525,     0.24625,     0.24725,     0.24825,     0.24925,     0.25025,     0.25125,     0.25225,     0.25325,     0.25425,     0.25526,     0.25626,     0.25726,     0.25826,     0.25926,     0.26026,     0.26126,     0.26226,     0.26326,\n", "           0.26426,     0.26527,     0.26627,     0.26727,     0.26827,     0.26927,     0.27027,     0.27127,     0.27227,     0.27327,     0.27427,     0.27528,     0.27628,     0.27728,     0.27828,     0.27928,     0.28028,     0.28128,     0.28228,     0.28328,     0.28428,     0.28529,     0.28629,     0.28729,\n", "           0.28829,     0.28929,     0.29029,     0.29129,     0.29229,     0.29329,     0.29429,      0.2953,      0.2963,      0.2973,      0.2983,      0.2993,      0.3003,      0.3013,      0.3023,      0.3033,      0.3043,     0.30531,     0.30631,     0.30731,     0.30831,     0.30931,     0.31031,     0.31131,\n", "           0.31231,     0.31331,     0.31431,     0.31532,     0.31632,     0.31732,     0.31832,     0.31932,     0.32032,     0.32132,     0.32232,     0.32332,     0.32432,     0.32533,     0.32633,     0.32733,     0.32833,     0.32933,     0.33033,     0.33133,     0.33233,     0.33333,     0.33433,     0.33534,\n", "           0.33634,     0.33734,     0.33834,     0.33934,     0.34034,     0.34134,     0.34234,     0.34334,     0.34434,     0.34535,     0.34635,     0.34735,     0.34835,     0.34935,     0.35035,     0.35135,     0.35235,     0.35335,     0.35435,     0.35536,     0.35636,     0.35736,     0.35836,     0.35936,\n", "           0.36036,     0.36136,     0.36236,     0.36336,     0.36436,     0.36537,     0.36637,     0.36737,     0.36837,     0.36937,     0.37037,     0.37137,     0.37237,     0.37337,     0.37437,     0.37538,     0.37638,     0.37738,     0.37838,     0.37938,     0.38038,     0.38138,     0.38238,     0.38338,\n", "           0.38438,     0.38539,     0.38639,     0.38739,     0.38839,     0.38939,     0.39039,     0.39139,     0.39239,     0.39339,     0.39439,      0.3954,      0.3964,      0.3974,      0.3984,      0.3994,      0.4004,      0.4014,      0.4024,      0.4034,      0.4044,     0.40541,     0.40641,     0.40741,\n", "           0.40841,     0.40941,     0.41041,     0.41141,     0.41241,     0.41341,     0.41441,     0.41542,     0.41642,     0.41742,     0.41842,     0.41942,     0.42042,     0.42142,     0.42242,     0.42342,     0.42442,     0.42543,     0.42643,     0.42743,     0.42843,     0.42943,     0.43043,     0.43143,\n", "           0.43243,     0.43343,     0.43443,     0.43544,     0.43644,     0.43744,     0.43844,     0.43944,     0.44044,     0.44144,     0.44244,     0.44344,     0.44444,     0.44545,     0.44645,     0.44745,     0.44845,     0.44945,     0.45045,     0.45145,     0.45245,     0.45345,     0.45445,     0.45546,\n", "           0.45646,     0.45746,     0.45846,     0.45946,     0.46046,     0.46146,     0.46246,     0.46346,     0.46446,     0.46547,     0.46647,     0.46747,     0.46847,     0.46947,     0.47047,     0.47147,     0.47247,     0.47347,     0.47447,     0.47548,     0.47648,     0.47748,     0.47848,     0.47948,\n", "           0.48048,     0.48148,     0.48248,     0.48348,     0.48448,     0.48549,     0.48649,     0.48749,     0.48849,     0.48949,     0.49049,     0.49149,     0.49249,     0.49349,     0.49449,      0.4955,      0.4965,      0.4975,      0.4985,      0.4995,      0.5005,      0.5015,      0.5025,      0.5035,\n", "            0.5045,     0.50551,     0.50651,     0.50751,     0.50851,     0.50951,     0.51051,     0.51151,     0.51251,     0.51351,     0.51451,     0.51552,     0.51652,     0.51752,     0.51852,     0.51952,     0.52052,     0.52152,     0.52252,     0.52352,     0.52452,     0.52553,     0.52653,     0.52753,\n", "           0.52853,     0.52953,     0.53053,     0.53153,     0.53253,     0.53353,     0.53453,     0.53554,     0.53654,     0.53754,     0.53854,     0.53954,     0.54054,     0.54154,     0.54254,     0.54354,     0.54454,     0.54555,     0.54655,     0.54755,     0.54855,     0.54955,     0.55055,     0.55155,\n", "           0.55255,     0.55355,     0.55455,     0.55556,     0.55656,     0.55756,     0.55856,     0.55956,     0.56056,     0.56156,     0.56256,     0.56356,     0.56456,     0.56557,     0.56657,     0.56757,     0.56857,     0.56957,     0.57057,     0.57157,     0.57257,     0.57357,     0.57457,     0.57558,\n", "           0.57658,     0.57758,     0.57858,     0.57958,     0.58058,     0.58158,     0.58258,     0.58358,     0.58458,     0.58559,     0.58659,     0.58759,     0.58859,     0.58959,     0.59059,     0.59159,     0.59259,     0.59359,     0.59459,      0.5956,      0.5966,      0.5976,      0.5986,      0.5996,\n", "            0.6006,      0.6016,      0.6026,      0.6036,      0.6046,     0.60561,     0.60661,     0.60761,     0.60861,     0.60961,     0.61061,     0.61161,     0.61261,     0.61361,     0.61461,     0.61562,     0.61662,     0.61762,     0.61862,     0.61962,     0.62062,     0.62162,     0.62262,     0.62362,\n", "           0.62462,     0.62563,     0.62663,     0.62763,     0.62863,     0.62963,     0.63063,     0.63163,     0.63263,     0.63363,     0.63463,     0.63564,     0.63664,     0.63764,     0.63864,     0.63964,     0.64064,     0.64164,     0.64264,     0.64364,     0.64464,     0.64565,     0.64665,     0.64765,\n", "           0.64865,     0.64965,     0.65065,     0.65165,     0.65265,     0.65365,     0.65465,     0.65566,     0.65666,     0.65766,     0.65866,     0.65966,     0.66066,     0.66166,     0.66266,     0.66366,     0.66466,     0.66567,     0.66667,     0.66767,     0.66867,     0.66967,     0.67067,     0.67167,\n", "           0.67267,     0.67367,     0.67467,     0.67568,     0.67668,     0.67768,     0.67868,     0.67968,     0.68068,     0.68168,     0.68268,     0.68368,     0.68468,     0.68569,     0.68669,     0.68769,     0.68869,     0.68969,     0.69069,     0.69169,     0.69269,     0.69369,     0.69469,      0.6957,\n", "            0.6967,      0.6977,      0.6987,      0.6997,      0.7007,      0.7017,      0.7027,      0.7037,      0.7047,     0.70571,     0.70671,     0.70771,     0.70871,     0.70971,     0.71071,     0.71171,     0.71271,     0.71371,     0.71471,     0.71572,     0.71672,     0.71772,     0.71872,     0.71972,\n", "           0.72072,     0.72172,     0.72272,     0.72372,     0.72472,     0.72573,     0.72673,     0.72773,     0.72873,     0.72973,     0.73073,     0.73173,     0.73273,     0.73373,     0.73473,     0.73574,     0.73674,     0.73774,     0.73874,     0.73974,     0.74074,     0.74174,     0.74274,     0.74374,\n", "           0.74474,     0.74575,     0.74675,     0.74775,     0.74875,     0.74975,     0.75075,     0.75175,     0.75275,     0.75375,     0.75475,     0.75576,     0.75676,     0.75776,     0.75876,     0.75976,     0.76076,     0.76176,     0.76276,     0.76376,     0.76476,     0.76577,     0.76677,     0.76777,\n", "           0.76877,     0.76977,     0.77077,     0.77177,     0.77277,     0.77377,     0.77477,     0.77578,     0.77678,     0.77778,     0.77878,     0.77978,     0.78078,     0.78178,     0.78278,     0.78378,     0.78478,     0.78579,     0.78679,     0.78779,     0.78879,     0.78979,     0.79079,     0.79179,\n", "           0.79279,     0.79379,     0.79479,      0.7958,      0.7968,      0.7978,      0.7988,      0.7998,      0.8008,      0.8018,      0.8028,      0.8038,      0.8048,     0.80581,     0.80681,     0.80781,     0.80881,     0.80981,     0.81081,     0.81181,     0.81281,     0.81381,     0.81481,     0.81582,\n", "           0.81682,     0.81782,     0.81882,     0.81982,     0.82082,     0.82182,     0.82282,     0.82382,     0.82482,     0.82583,     0.82683,     0.82783,     0.82883,     0.82983,     0.83083,     0.83183,     0.83283,     0.83383,     0.83483,     0.83584,     0.83684,     0.83784,     0.83884,     0.83984,\n", "           0.84084,     0.84184,     0.84284,     0.84384,     0.84484,     0.84585,     0.84685,     0.84785,     0.84885,     0.84985,     0.85085,     0.85185,     0.85285,     0.85385,     0.85485,     0.85586,     0.85686,     0.85786,     0.85886,     0.85986,     0.86086,     0.86186,     0.86286,     0.86386,\n", "           0.86486,     0.86587,     0.86687,     0.86787,     0.86887,     0.86987,     0.87087,     0.87187,     0.87287,     0.87387,     0.87487,     0.87588,     0.87688,     0.87788,     0.87888,     0.87988,     0.88088,     0.88188,     0.88288,     0.88388,     0.88488,     0.88589,     0.88689,     0.88789,\n", "           0.88889,     0.88989,     0.89089,     0.89189,     0.89289,     0.89389,     0.89489,      0.8959,      0.8969,      0.8979,      0.8989,      0.8999,      0.9009,      0.9019,      0.9029,      0.9039,      0.9049,     0.90591,     0.90691,     0.90791,     0.90891,     0.90991,     0.91091,     0.91191,\n", "           0.91291,     0.91391,     0.91491,     0.91592,     0.91692,     0.91792,     0.91892,     0.91992,     0.92092,     0.92192,     0.92292,     0.92392,     0.92492,     0.92593,     0.92693,     0.92793,     0.92893,     0.92993,     0.93093,     0.93193,     0.93293,     0.93393,     0.93493,     0.93594,\n", "           0.93694,     0.93794,     0.93894,     0.93994,     0.94094,     0.94194,     0.94294,     0.94394,     0.94494,     0.94595,     0.94695,     0.94795,     0.94895,     0.94995,     0.95095,     0.95195,     0.95295,     0.95395,     0.95495,     0.95596,     0.95696,     0.95796,     0.95896,     0.95996,\n", "           0.96096,     0.96196,     0.96296,     0.96396,     0.96496,     0.96597,     0.96697,     0.96797,     0.96897,     0.96997,     0.97097,     0.97197,     0.97297,     0.97397,     0.97497,     0.97598,     0.97698,     0.97798,     0.97898,     0.97998,     0.98098,     0.98198,     0.98298,     0.98398,\n", "           0.98498,     0.98599,     0.98699,     0.98799,     0.98899,     0.98999,     0.99099,     0.99199,     0.99299,     0.99399,     0.99499,       0.996,       0.997,       0.998,       0.999,           1]), array([[          1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,     0.97701,     0.97701,     0.97701,     0.97701,     0.97701,     0.97701,     0.65019,     0.65019,     0.65019,     0.65019,     0.65019,     0.65019,     0.58586,\n", "             0.5326,     0.47934,     0.42608,     0.37282,     0.31956,      0.2663,     0.21304,     0.15978,     0.10652,     0.05326,           0]]), 'Recall', 'Precision'], [array([          0,    0.001001,    0.002002,    0.003003,    0.004004,    0.005005,    0.006006,    0.007007,    0.008008,    0.009009,     0.01001,    0.011011,    0.012012,    0.013013,    0.014014,    0.015015,    0.016016,    0.017017,    0.018018,    0.019019,     0.02002,    0.021021,    0.022022,    0.023023,\n", "          0.024024,    0.025025,    0.026026,    0.027027,    0.028028,    0.029029,     0.03003,    0.031031,    0.032032,    0.033033,    0.034034,    0.035035,    0.036036,    0.037037,    0.038038,    0.039039,     0.04004,    0.041041,    0.042042,    0.043043,    0.044044,    0.045045,    0.046046,    0.047047,\n", "          0.048048,    0.049049,     0.05005,    0.051051,    0.052052,    0.053053,    0.054054,    0.055055,    0.056056,    0.057057,    0.058058,    0.059059,     0.06006,    0.061061,    0.062062,    0.063063,    0.064064,    0.065065,    0.066066,    0.067067,    0.068068,    0.069069,     0.07007,    0.071071,\n", "          0.072072,    0.073073,    0.074074,    0.075075,    0.076076,    0.077077,    0.078078,    0.079079,     0.08008,    0.081081,    0.082082,    0.083083,    0.084084,    0.085085,    0.086086,    0.087087,    0.088088,    0.089089,     0.09009,    0.091091,    0.092092,    0.093093,    0.094094,    0.095095,\n", "          0.096096,    0.097097,    0.098098,    0.099099,      0.1001,      0.1011,      0.1021,      0.1031,      0.1041,     0.10511,     0.10611,     0.10711,     0.10811,     0.10911,     0.11011,     0.11111,     0.11211,     0.11311,     0.11411,     0.11512,     0.11612,     0.11712,     0.11812,     0.11912,\n", "           0.12012,     0.12112,     0.12212,     0.12312,     0.12412,     0.12513,     0.12613,     0.12713,     0.12813,     0.12913,     0.13013,     0.13113,     0.13213,     0.13313,     0.13413,     0.13514,     0.13614,     0.13714,     0.13814,     0.13914,     0.14014,     0.14114,     0.14214,     0.14314,\n", "           0.14414,     0.14515,     0.14615,     0.14715,     0.14815,     0.14915,     0.15015,     0.15115,     0.15215,     0.15315,     0.15415,     0.15516,     0.15616,     0.15716,     0.15816,     0.15916,     0.16016,     0.16116,     0.16216,     0.16316,     0.16416,     0.16517,     0.16617,     0.16717,\n", "           0.16817,     0.16917,     0.17017,     0.17117,     0.17217,     0.17317,     0.17417,     0.17518,     0.17618,     0.17718,     0.17818,     0.17918,     0.18018,     0.18118,     0.18218,     0.18318,     0.18418,     0.18519,     0.18619,     0.18719,     0.18819,     0.18919,     0.19019,     0.19119,\n", "           0.19219,     0.19319,     0.19419,      0.1952,      0.1962,      0.1972,      0.1982,      0.1992,      0.2002,      0.2012,      0.2022,      0.2032,      0.2042,     0.20521,     0.20621,     0.20721,     0.20821,     0.20921,     0.21021,     0.21121,     0.21221,     0.21321,     0.21421,     0.21522,\n", "           0.21622,     0.21722,     0.21822,     0.21922,     0.22022,     0.22122,     0.22222,     0.22322,     0.22422,     0.22523,     0.22623,     0.22723,     0.22823,     0.22923,     0.23023,     0.23123,     0.23223,     0.23323,     0.23423,     0.23524,     0.23624,     0.23724,     0.23824,     0.23924,\n", "           0.24024,     0.24124,     0.24224,     0.24324,     0.24424,     0.24525,     0.24625,     0.24725,     0.24825,     0.24925,     0.25025,     0.25125,     0.25225,     0.25325,     0.25425,     0.25526,     0.25626,     0.25726,     0.25826,     0.25926,     0.26026,     0.26126,     0.26226,     0.26326,\n", "           0.26426,     0.26527,     0.26627,     0.26727,     0.26827,     0.26927,     0.27027,     0.27127,     0.27227,     0.27327,     0.27427,     0.27528,     0.27628,     0.27728,     0.27828,     0.27928,     0.28028,     0.28128,     0.28228,     0.28328,     0.28428,     0.28529,     0.28629,     0.28729,\n", "           0.28829,     0.28929,     0.29029,     0.29129,     0.29229,     0.29329,     0.29429,      0.2953,      0.2963,      0.2973,      0.2983,      0.2993,      0.3003,      0.3013,      0.3023,      0.3033,      0.3043,     0.30531,     0.30631,     0.30731,     0.30831,     0.30931,     0.31031,     0.31131,\n", "           0.31231,     0.31331,     0.31431,     0.31532,     0.31632,     0.31732,     0.31832,     0.31932,     0.32032,     0.32132,     0.32232,     0.32332,     0.32432,     0.32533,     0.32633,     0.32733,     0.32833,     0.32933,     0.33033,     0.33133,     0.33233,     0.33333,     0.33433,     0.33534,\n", "           0.33634,     0.33734,     0.33834,     0.33934,     0.34034,     0.34134,     0.34234,     0.34334,     0.34434,     0.34535,     0.34635,     0.34735,     0.34835,     0.34935,     0.35035,     0.35135,     0.35235,     0.35335,     0.35435,     0.35536,     0.35636,     0.35736,     0.35836,     0.35936,\n", "           0.36036,     0.36136,     0.36236,     0.36336,     0.36436,     0.36537,     0.36637,     0.36737,     0.36837,     0.36937,     0.37037,     0.37137,     0.37237,     0.37337,     0.37437,     0.37538,     0.37638,     0.37738,     0.37838,     0.37938,     0.38038,     0.38138,     0.38238,     0.38338,\n", "           0.38438,     0.38539,     0.38639,     0.38739,     0.38839,     0.38939,     0.39039,     0.39139,     0.39239,     0.39339,     0.39439,      0.3954,      0.3964,      0.3974,      0.3984,      0.3994,      0.4004,      0.4014,      0.4024,      0.4034,      0.4044,     0.40541,     0.40641,     0.40741,\n", "           0.40841,     0.40941,     0.41041,     0.41141,     0.41241,     0.41341,     0.41441,     0.41542,     0.41642,     0.41742,     0.41842,     0.41942,     0.42042,     0.42142,     0.42242,     0.42342,     0.42442,     0.42543,     0.42643,     0.42743,     0.42843,     0.42943,     0.43043,     0.43143,\n", "           0.43243,     0.43343,     0.43443,     0.43544,     0.43644,     0.43744,     0.43844,     0.43944,     0.44044,     0.44144,     0.44244,     0.44344,     0.44444,     0.44545,     0.44645,     0.44745,     0.44845,     0.44945,     0.45045,     0.45145,     0.45245,     0.45345,     0.45445,     0.45546,\n", "           0.45646,     0.45746,     0.45846,     0.45946,     0.46046,     0.46146,     0.46246,     0.46346,     0.46446,     0.46547,     0.46647,     0.46747,     0.46847,     0.46947,     0.47047,     0.47147,     0.47247,     0.47347,     0.47447,     0.47548,     0.47648,     0.47748,     0.47848,     0.47948,\n", "           0.48048,     0.48148,     0.48248,     0.48348,     0.48448,     0.48549,     0.48649,     0.48749,     0.48849,     0.48949,     0.49049,     0.49149,     0.49249,     0.49349,     0.49449,      0.4955,      0.4965,      0.4975,      0.4985,      0.4995,      0.5005,      0.5015,      0.5025,      0.5035,\n", "            0.5045,     0.50551,     0.50651,     0.50751,     0.50851,     0.50951,     0.51051,     0.51151,     0.51251,     0.51351,     0.51451,     0.51552,     0.51652,     0.51752,     0.51852,     0.51952,     0.52052,     0.52152,     0.52252,     0.52352,     0.52452,     0.52553,     0.52653,     0.52753,\n", "           0.52853,     0.52953,     0.53053,     0.53153,     0.53253,     0.53353,     0.53453,     0.53554,     0.53654,     0.53754,     0.53854,     0.53954,     0.54054,     0.54154,     0.54254,     0.54354,     0.54454,     0.54555,     0.54655,     0.54755,     0.54855,     0.54955,     0.55055,     0.55155,\n", "           0.55255,     0.55355,     0.55455,     0.55556,     0.55656,     0.55756,     0.55856,     0.55956,     0.56056,     0.56156,     0.56256,     0.56356,     0.56456,     0.56557,     0.56657,     0.56757,     0.56857,     0.56957,     0.57057,     0.57157,     0.57257,     0.57357,     0.57457,     0.57558,\n", "           0.57658,     0.57758,     0.57858,     0.57958,     0.58058,     0.58158,     0.58258,     0.58358,     0.58458,     0.58559,     0.58659,     0.58759,     0.58859,     0.58959,     0.59059,     0.59159,     0.59259,     0.59359,     0.59459,      0.5956,      0.5966,      0.5976,      0.5986,      0.5996,\n", "            0.6006,      0.6016,      0.6026,      0.6036,      0.6046,     0.60561,     0.60661,     0.60761,     0.60861,     0.60961,     0.61061,     0.61161,     0.61261,     0.61361,     0.61461,     0.61562,     0.61662,     0.61762,     0.61862,     0.61962,     0.62062,     0.62162,     0.62262,     0.62362,\n", "           0.62462,     0.62563,     0.62663,     0.62763,     0.62863,     0.62963,     0.63063,     0.63163,     0.63263,     0.63363,     0.63463,     0.63564,     0.63664,     0.63764,     0.63864,     0.63964,     0.64064,     0.64164,     0.64264,     0.64364,     0.64464,     0.64565,     0.64665,     0.64765,\n", "           0.64865,     0.64965,     0.65065,     0.65165,     0.65265,     0.65365,     0.65465,     0.65566,     0.65666,     0.65766,     0.65866,     0.65966,     0.66066,     0.66166,     0.66266,     0.66366,     0.66466,     0.66567,     0.66667,     0.66767,     0.66867,     0.66967,     0.67067,     0.67167,\n", "           0.67267,     0.67367,     0.67467,     0.67568,     0.67668,     0.67768,     0.67868,     0.67968,     0.68068,     0.68168,     0.68268,     0.68368,     0.68468,     0.68569,     0.68669,     0.68769,     0.68869,     0.68969,     0.69069,     0.69169,     0.69269,     0.69369,     0.69469,      0.6957,\n", "            0.6967,      0.6977,      0.6987,      0.6997,      0.7007,      0.7017,      0.7027,      0.7037,      0.7047,     0.70571,     0.70671,     0.70771,     0.70871,     0.70971,     0.71071,     0.71171,     0.71271,     0.71371,     0.71471,     0.71572,     0.71672,     0.71772,     0.71872,     0.71972,\n", "           0.72072,     0.72172,     0.72272,     0.72372,     0.72472,     0.72573,     0.72673,     0.72773,     0.72873,     0.72973,     0.73073,     0.73173,     0.73273,     0.73373,     0.73473,     0.73574,     0.73674,     0.73774,     0.73874,     0.73974,     0.74074,     0.74174,     0.74274,     0.74374,\n", "           0.74474,     0.74575,     0.74675,     0.74775,     0.74875,     0.74975,     0.75075,     0.75175,     0.75275,     0.75375,     0.75475,     0.75576,     0.75676,     0.75776,     0.75876,     0.75976,     0.76076,     0.76176,     0.76276,     0.76376,     0.76476,     0.76577,     0.76677,     0.76777,\n", "           0.76877,     0.76977,     0.77077,     0.77177,     0.77277,     0.77377,     0.77477,     0.77578,     0.77678,     0.77778,     0.77878,     0.77978,     0.78078,     0.78178,     0.78278,     0.78378,     0.78478,     0.78579,     0.78679,     0.78779,     0.78879,     0.78979,     0.79079,     0.79179,\n", "           0.79279,     0.79379,     0.79479,      0.7958,      0.7968,      0.7978,      0.7988,      0.7998,      0.8008,      0.8018,      0.8028,      0.8038,      0.8048,     0.80581,     0.80681,     0.80781,     0.80881,     0.80981,     0.81081,     0.81181,     0.81281,     0.81381,     0.81481,     0.81582,\n", "           0.81682,     0.81782,     0.81882,     0.81982,     0.82082,     0.82182,     0.82282,     0.82382,     0.82482,     0.82583,     0.82683,     0.82783,     0.82883,     0.82983,     0.83083,     0.83183,     0.83283,     0.83383,     0.83483,     0.83584,     0.83684,     0.83784,     0.83884,     0.83984,\n", "           0.84084,     0.84184,     0.84284,     0.84384,     0.84484,     0.84585,     0.84685,     0.84785,     0.84885,     0.84985,     0.85085,     0.85185,     0.85285,     0.85385,     0.85485,     0.85586,     0.85686,     0.85786,     0.85886,     0.85986,     0.86086,     0.86186,     0.86286,     0.86386,\n", "           0.86486,     0.86587,     0.86687,     0.86787,     0.86887,     0.86987,     0.87087,     0.87187,     0.87287,     0.87387,     0.87487,     0.87588,     0.87688,     0.87788,     0.87888,     0.87988,     0.88088,     0.88188,     0.88288,     0.88388,     0.88488,     0.88589,     0.88689,     0.88789,\n", "           0.88889,     0.88989,     0.89089,     0.89189,     0.89289,     0.89389,     0.89489,      0.8959,      0.8969,      0.8979,      0.8989,      0.8999,      0.9009,      0.9019,      0.9029,      0.9039,      0.9049,     0.90591,     0.90691,     0.90791,     0.90891,     0.90991,     0.91091,     0.91191,\n", "           0.91291,     0.91391,     0.91491,     0.91592,     0.91692,     0.91792,     0.91892,     0.91992,     0.92092,     0.92192,     0.92292,     0.92392,     0.92492,     0.92593,     0.92693,     0.92793,     0.92893,     0.92993,     0.93093,     0.93193,     0.93293,     0.93393,     0.93493,     0.93594,\n", "           0.93694,     0.93794,     0.93894,     0.93994,     0.94094,     0.94194,     0.94294,     0.94394,     0.94494,     0.94595,     0.94695,     0.94795,     0.94895,     0.94995,     0.95095,     0.95195,     0.95295,     0.95395,     0.95495,     0.95596,     0.95696,     0.95796,     0.95896,     0.95996,\n", "           0.96096,     0.96196,     0.96296,     0.96396,     0.96496,     0.96597,     0.96697,     0.96797,     0.96897,     0.96997,     0.97097,     0.97197,     0.97297,     0.97397,     0.97497,     0.97598,     0.97698,     0.97798,     0.97898,     0.97998,     0.98098,     0.98198,     0.98298,     0.98398,\n", "           0.98498,     0.98599,     0.98699,     0.98799,     0.98899,     0.98999,     0.99099,     0.99199,     0.99299,     0.99399,     0.99499,       0.996,       0.997,       0.998,       0.999,           1]), array([[    0.75831,     0.75831,     0.80102,     0.83741,     0.86144,     0.87119,      0.8843,     0.89391,     0.90096,     0.90526,     0.90698,     0.91057,     0.91435,     0.91633,     0.92075,     0.92456,      0.9262,     0.92948,     0.93126,     0.93199,     0.93255,     0.93311,     0.93368,\n", "            0.93443,     0.93562,     0.93667,     0.93689,     0.93712,     0.93734,     0.93756,     0.93779,     0.93801,     0.93823,     0.93846,     0.93868,      0.9389,     0.93912,     0.93939,      0.9397,        0.94,     0.94031,     0.94062,     0.94092,     0.94123,     0.94153,     0.94193,\n", "            0.94451,     0.94485,     0.94519,     0.94553,     0.94587,     0.94621,     0.94655,     0.94689,     0.94763,     0.94882,     0.95072,     0.95812,     0.95902,     0.95991,     0.96057,     0.96088,     0.96119,      0.9615,      0.9618,     0.96211,     0.96242,     0.96272,     0.96303,\n", "            0.96327,     0.96346,     0.96365,     0.96384,     0.96403,     0.96422,     0.96441,      0.9646,     0.96479,     0.96498,     0.96517,     0.96536,     0.96555,     0.96573,     0.96592,       0.966,     0.96608,     0.96616,     0.96624,     0.96632,      0.9664,     0.96648,     0.96656,\n", "            0.96664,     0.96672,      0.9668,     0.96688,     0.96696,     0.96705,     0.96713,     0.96721,     0.96729,     0.96737,     0.96745,     0.96753,     0.96761,     0.96769,     0.96777,     0.96785,     0.96793,     0.96801,     0.96809,     0.96817,     0.96825,     0.96833,     0.96841,\n", "            0.96849,     0.96857,     0.96866,     0.96878,      0.9689,     0.96903,     0.96915,     0.96928,      0.9694,     0.96953,     0.96965,     0.96978,      0.9699,     0.97003,     0.97015,     0.97028,      0.9704,     0.97052,     0.97065,     0.97077,      0.9709,     0.97102,     0.97115,\n", "            0.97127,      0.9714,      0.9717,     0.97206,     0.97242,     0.97278,     0.97315,     0.97351,     0.97387,     0.97424,     0.97471,     0.97518,     0.97564,     0.97611,     0.97658,     0.97702,     0.97718,     0.97733,     0.97748,     0.97763,     0.97778,     0.97794,     0.97809,\n", "            0.97824,     0.97839,     0.97854,      0.9787,     0.97885,       0.979,     0.97915,      0.9793,     0.97945,     0.97961,     0.97976,     0.97979,     0.97971,     0.97964,     0.97957,      0.9795,     0.97942,     0.97935,     0.97928,      0.9792,     0.97913,     0.97906,     0.97899,\n", "            0.97891,     0.97884,     0.97877,     0.97869,     0.97862,     0.97855,     0.97847,      0.9784,     0.97833,     0.97826,     0.97818,     0.97811,     0.97804,     0.97796,     0.97789,     0.97782,     0.97774,     0.97767,      0.9776,     0.97752,     0.97745,     0.97738,     0.97731,\n", "            0.97723,     0.97716,     0.97709,     0.97701,     0.97694,     0.97691,     0.97707,     0.97723,      0.9774,     0.97756,     0.97772,     0.97788,     0.97805,     0.97821,     0.97837,     0.97853,      0.9787,     0.97886,     0.97902,     0.97918,     0.97934,     0.97951,     0.97967,\n", "            0.97973,     0.97975,     0.97977,     0.97979,     0.97981,     0.97983,     0.97985,     0.97987,     0.97989,     0.97992,     0.97994,     0.97996,     0.97998,        0.98,     0.98002,     0.98004,     0.98006,     0.98008,     0.98011,     0.98013,     0.98015,     0.98017,     0.98019,\n", "            0.98021,     0.98023,     0.98025,     0.98028,      0.9803,     0.98032,     0.98034,     0.98036,     0.98038,      0.9804,     0.98042,     0.98044,     0.98047,     0.98049,     0.98051,     0.98053,     0.98055,     0.98057,     0.98059,     0.98061,     0.98063,     0.98065,     0.98068,\n", "             0.9807,     0.98072,     0.98074,     0.98076,     0.98078,      0.9808,     0.98082,     0.98084,     0.98087,     0.98089,     0.98091,     0.98093,     0.98095,     0.98097,     0.98099,     0.98101,     0.98103,     0.98106,     0.98108,      0.9811,     0.98112,     0.98114,     0.98116,\n", "            0.98118,      0.9812,     0.98122,     0.98125,     0.98127,     0.98129,     0.98131,     0.98133,     0.98135,     0.98137,     0.98139,     0.98141,     0.98143,     0.98146,     0.98148,      0.9815,     0.98152,     0.98154,     0.98156,     0.98158,      0.9816,     0.98162,     0.98165,\n", "            0.98167,     0.98169,     0.98171,     0.98173,     0.98175,     0.98177,     0.98179,     0.98181,     0.98184,     0.98186,     0.98188,      0.9819,     0.98192,     0.98194,     0.98196,     0.98198,       0.982,     0.98202,     0.98205,     0.98207,     0.98209,     0.98211,     0.98213,\n", "            0.98215,     0.98217,     0.98219,     0.98221,     0.98223,     0.98226,     0.98228,      0.9823,     0.98232,     0.98234,     0.98236,     0.98238,      0.9824,     0.98242,     0.98245,     0.98247,     0.98249,     0.98251,     0.98253,     0.98255,     0.98257,     0.98258,     0.98259,\n", "             0.9826,     0.98261,     0.98262,     0.98263,     0.98265,     0.98266,     0.98267,     0.98268,     0.98269,      0.9827,     0.98272,     0.98273,     0.98274,     0.98275,     0.98276,     0.98277,     0.98278,      0.9828,     0.98281,     0.98282,     0.98283,     0.98284,     0.98285,\n", "            0.98286,     0.98288,     0.98289,      0.9829,     0.98291,     0.98292,     0.98293,     0.98295,     0.98296,     0.98297,     0.98298,     0.98299,       0.983,     0.98301,     0.98303,     0.98304,     0.98305,     0.98306,     0.98307,     0.98308,      0.9831,     0.98311,     0.98312,\n", "            0.98313,     0.98314,     0.98315,     0.98316,     0.98318,     0.98319,      0.9832,     0.98321,     0.98322,     0.98323,     0.98324,     0.98326,     0.98327,     0.98328,     0.98329,      0.9833,     0.98331,     0.98333,     0.98334,     0.98335,     0.98336,     0.98337,     0.98338,\n", "            0.98339,     0.98341,     0.98342,     0.98343,     0.98344,     0.98345,     0.98346,     0.98347,     0.98349,      0.9835,     0.98351,     0.98352,     0.98353,     0.98354,     0.98356,     0.98357,     0.98358,     0.98359,      0.9836,     0.98361,     0.98362,     0.98364,     0.98365,\n", "            0.98366,     0.98367,     0.98368,     0.98369,     0.98371,     0.98372,     0.98373,     0.98374,     0.98375,     0.98376,     0.98377,     0.98379,      0.9838,     0.98381,     0.98382,     0.98383,     0.98384,     0.98385,     0.98387,     0.98388,     0.98389,      0.9839,     0.98391,\n", "            0.98392,     0.98393,     0.98395,     0.98396,     0.98397,     0.98398,     0.98399,       0.984,     0.98402,     0.98403,     0.98404,     0.98405,     0.98406,     0.98407,     0.98408,      0.9841,     0.98411,     0.98412,     0.98413,     0.98414,     0.98415,     0.98416,     0.98418,\n", "            0.98419,      0.9842,     0.98421,     0.98422,     0.98423,     0.98425,     0.98426,     0.98427,     0.98428,     0.98429,      0.9843,     0.98431,     0.98433,     0.98434,     0.98435,     0.98436,     0.98437,     0.98438,     0.98439,     0.98441,     0.98442,     0.98443,     0.98444,\n", "            0.98445,     0.98446,     0.98447,     0.98449,      0.9845,     0.98451,     0.98452,     0.98453,     0.98454,     0.98456,     0.98457,     0.98458,     0.98459,      0.9846,     0.98461,     0.98462,     0.98464,     0.98465,     0.98466,     0.98467,     0.98468,     0.98469,      0.9847,\n", "            0.98472,     0.98473,     0.98474,     0.98475,     0.98476,     0.98477,     0.98478,      0.9848,     0.98481,     0.98482,     0.98483,     0.98484,     0.98485,     0.98487,     0.98488,     0.98489,      0.9849,     0.98491,     0.98492,     0.98493,     0.98495,     0.98496,     0.98497,\n", "            0.98498,     0.98499,       0.985,     0.98501,     0.98503,     0.98504,     0.98505,     0.98506,     0.98507,     0.98508,     0.98509,     0.98511,     0.98512,     0.98513,     0.98514,     0.98515,     0.98516,     0.98517,     0.98519,      0.9852,     0.98521,     0.98522,     0.98523,\n", "            0.98524,     0.98525,     0.98527,     0.98528,     0.98529,      0.9853,     0.98531,     0.98532,     0.98534,     0.98535,     0.98536,     0.98537,     0.98538,     0.98539,      0.9854,     0.98542,     0.98561,     0.98615,     0.98669,     0.98723,     0.98776,      0.9883,     0.98828,\n", "            0.98825,     0.98823,     0.98821,     0.98818,     0.98816,     0.98813,     0.98811,     0.98808,     0.98806,     0.98803,     0.98801,     0.98798,     0.98796,     0.98793,     0.98791,     0.98788,     0.98786,     0.98783,     0.98781,     0.98778,     0.98776,     0.98773,     0.98771,\n", "            0.98769,     0.98766,     0.98764,     0.98761,     0.98759,     0.98756,     0.98754,     0.98751,     0.98749,     0.98746,     0.98744,     0.98741,     0.98739,     0.98736,     0.98734,     0.98731,     0.98729,     0.98726,     0.98724,     0.98721,     0.98719,     0.98716,     0.98714,\n", "            0.98711,     0.98709,     0.98707,     0.98704,     0.98702,     0.98699,     0.98697,     0.98694,     0.98692,     0.98689,     0.98687,     0.98684,     0.98682,     0.98679,     0.98677,     0.98674,     0.98672,     0.98669,     0.98667,     0.98664,     0.98662,     0.98659,     0.98657,\n", "            0.98654,     0.98652,     0.98649,     0.98647,     0.98644,     0.98642,     0.98639,     0.98637,     0.98635,     0.98632,      0.9863,     0.98627,     0.98625,     0.98622,      0.9862,     0.98617,     0.98615,     0.98612,      0.9861,     0.98607,     0.98605,     0.98602,       0.986,\n", "            0.98597,     0.98595,     0.98592,      0.9859,     0.98587,     0.98585,     0.98582,      0.9858,     0.98577,     0.98575,     0.98572,      0.9857,     0.98567,     0.98565,     0.98562,      0.9856,     0.98557,     0.98555,     0.98552,      0.9855,     0.98547,     0.98545,     0.98542,\n", "             0.9854,     0.98538,     0.98535,     0.98533,      0.9853,     0.98528,     0.98525,     0.98523,      0.9852,     0.98518,     0.98515,     0.98513,      0.9851,     0.98508,     0.98505,     0.98503,     0.98501,     0.98498,     0.98496,     0.98493,     0.98491,     0.98488,     0.98486,\n", "            0.98483,     0.98481,     0.98478,     0.98476,     0.98473,     0.98471,     0.98469,     0.98466,     0.98464,     0.98461,     0.98459,     0.98456,     0.98454,     0.98451,     0.98449,     0.98446,     0.98444,     0.98441,     0.98439,     0.98437,     0.98434,     0.98432,     0.98429,\n", "            0.98427,     0.98424,     0.98422,     0.98419,     0.98417,     0.98414,     0.98412,     0.98409,     0.98407,     0.98404,     0.98402,       0.984,     0.98397,     0.98395,     0.98392,      0.9839,     0.98387,     0.98385,     0.98382,      0.9838,     0.98377,     0.98375,     0.98372,\n", "             0.9837,     0.98367,     0.98365,     0.98363,      0.9836,     0.98358,     0.98355,     0.98353,      0.9835,     0.98348,     0.98345,     0.98343,      0.9834,     0.98338,     0.98335,     0.98333,      0.9833,     0.98328,     0.98325,     0.98323,     0.98321,     0.98318,     0.98316,\n", "            0.98313,     0.98311,     0.98308,     0.98306,     0.98303,     0.98301,     0.98298,     0.98296,     0.98293,     0.98291,     0.98288,     0.98286,     0.98283,     0.98281,     0.98279,     0.98276,     0.98274,     0.98271,     0.98269,     0.98266,     0.98264,     0.98261,     0.98259,\n", "            0.98256,     0.98254,     0.98251,     0.98249,     0.98246,     0.98244,     0.98241,     0.98239,     0.98237,     0.98231,     0.98223,     0.98215,     0.98207,     0.98199,     0.98191,     0.98183,     0.98175,     0.98167,     0.98159,     0.98151,     0.98144,     0.98136,     0.98128,\n", "             0.9812,     0.98112,     0.98104,     0.98096,     0.98088,      0.9808,     0.98072,     0.98064,     0.98056,     0.98048,      0.9804,     0.98032,     0.98024,     0.98016,     0.98008,        0.98,     0.97992,     0.97984,     0.97976,     0.97968,      0.9796,     0.97952,     0.97944,\n", "            0.97936,     0.97922,     0.97909,     0.97896,     0.97882,     0.97869,     0.97855,     0.97842,     0.97828,     0.97815,     0.97801,     0.97788,     0.97775,     0.97761,     0.97748,     0.97734,     0.97721,     0.97707,     0.97694,      0.9768,     0.97667,     0.97653,      0.9764,\n", "            0.97618,     0.97588,     0.97559,     0.97529,       0.975,      0.9747,      0.9744,     0.97411,     0.97381,     0.97351,     0.97306,     0.97216,     0.97127,     0.97037,     0.96609,     0.96482,     0.96144,     0.95898,     0.95634,     0.95429,     0.95348,     0.95267,     0.95185,\n", "            0.95075,     0.94945,     0.94801,     0.94582,       0.944,     0.94234,     0.93375,     0.92622,     0.92135,     0.92037,     0.91938,     0.91791,     0.91559,     0.91221,     0.90869,     0.90342,     0.89634,     0.89094,     0.88352,     0.86129,     0.85355,     0.84571,     0.82193,\n", "            0.79716,     0.78427,      0.7655,     0.74068,     0.71591,     0.69631,      0.6289,     0.59436,     0.54775,     0.50347,     0.42835,      0.3989,     0.36832,     0.30393,     0.24344,     0.20633,     0.13829,    0.076714,    0.053454,    0.026807,    0.015205,           0,           0,\n", "                  0,           0,           0,           0,           0,           0,           0,           0,           0,           0,           0]]), 'Confidence', 'F1'], [array([          0,    0.001001,    0.002002,    0.003003,    0.004004,    0.005005,    0.006006,    0.007007,    0.008008,    0.009009,     0.01001,    0.011011,    0.012012,    0.013013,    0.014014,    0.015015,    0.016016,    0.017017,    0.018018,    0.019019,     0.02002,    0.021021,    0.022022,    0.023023,\n", "          0.024024,    0.025025,    0.026026,    0.027027,    0.028028,    0.029029,     0.03003,    0.031031,    0.032032,    0.033033,    0.034034,    0.035035,    0.036036,    0.037037,    0.038038,    0.039039,     0.04004,    0.041041,    0.042042,    0.043043,    0.044044,    0.045045,    0.046046,    0.047047,\n", "          0.048048,    0.049049,     0.05005,    0.051051,    0.052052,    0.053053,    0.054054,    0.055055,    0.056056,    0.057057,    0.058058,    0.059059,     0.06006,    0.061061,    0.062062,    0.063063,    0.064064,    0.065065,    0.066066,    0.067067,    0.068068,    0.069069,     0.07007,    0.071071,\n", "          0.072072,    0.073073,    0.074074,    0.075075,    0.076076,    0.077077,    0.078078,    0.079079,     0.08008,    0.081081,    0.082082,    0.083083,    0.084084,    0.085085,    0.086086,    0.087087,    0.088088,    0.089089,     0.09009,    0.091091,    0.092092,    0.093093,    0.094094,    0.095095,\n", "          0.096096,    0.097097,    0.098098,    0.099099,      0.1001,      0.1011,      0.1021,      0.1031,      0.1041,     0.10511,     0.10611,     0.10711,     0.10811,     0.10911,     0.11011,     0.11111,     0.11211,     0.11311,     0.11411,     0.11512,     0.11612,     0.11712,     0.11812,     0.11912,\n", "           0.12012,     0.12112,     0.12212,     0.12312,     0.12412,     0.12513,     0.12613,     0.12713,     0.12813,     0.12913,     0.13013,     0.13113,     0.13213,     0.13313,     0.13413,     0.13514,     0.13614,     0.13714,     0.13814,     0.13914,     0.14014,     0.14114,     0.14214,     0.14314,\n", "           0.14414,     0.14515,     0.14615,     0.14715,     0.14815,     0.14915,     0.15015,     0.15115,     0.15215,     0.15315,     0.15415,     0.15516,     0.15616,     0.15716,     0.15816,     0.15916,     0.16016,     0.16116,     0.16216,     0.16316,     0.16416,     0.16517,     0.16617,     0.16717,\n", "           0.16817,     0.16917,     0.17017,     0.17117,     0.17217,     0.17317,     0.17417,     0.17518,     0.17618,     0.17718,     0.17818,     0.17918,     0.18018,     0.18118,     0.18218,     0.18318,     0.18418,     0.18519,     0.18619,     0.18719,     0.18819,     0.18919,     0.19019,     0.19119,\n", "           0.19219,     0.19319,     0.19419,      0.1952,      0.1962,      0.1972,      0.1982,      0.1992,      0.2002,      0.2012,      0.2022,      0.2032,      0.2042,     0.20521,     0.20621,     0.20721,     0.20821,     0.20921,     0.21021,     0.21121,     0.21221,     0.21321,     0.21421,     0.21522,\n", "           0.21622,     0.21722,     0.21822,     0.21922,     0.22022,     0.22122,     0.22222,     0.22322,     0.22422,     0.22523,     0.22623,     0.22723,     0.22823,     0.22923,     0.23023,     0.23123,     0.23223,     0.23323,     0.23423,     0.23524,     0.23624,     0.23724,     0.23824,     0.23924,\n", "           0.24024,     0.24124,     0.24224,     0.24324,     0.24424,     0.24525,     0.24625,     0.24725,     0.24825,     0.24925,     0.25025,     0.25125,     0.25225,     0.25325,     0.25425,     0.25526,     0.25626,     0.25726,     0.25826,     0.25926,     0.26026,     0.26126,     0.26226,     0.26326,\n", "           0.26426,     0.26527,     0.26627,     0.26727,     0.26827,     0.26927,     0.27027,     0.27127,     0.27227,     0.27327,     0.27427,     0.27528,     0.27628,     0.27728,     0.27828,     0.27928,     0.28028,     0.28128,     0.28228,     0.28328,     0.28428,     0.28529,     0.28629,     0.28729,\n", "           0.28829,     0.28929,     0.29029,     0.29129,     0.29229,     0.29329,     0.29429,      0.2953,      0.2963,      0.2973,      0.2983,      0.2993,      0.3003,      0.3013,      0.3023,      0.3033,      0.3043,     0.30531,     0.30631,     0.30731,     0.30831,     0.30931,     0.31031,     0.31131,\n", "           0.31231,     0.31331,     0.31431,     0.31532,     0.31632,     0.31732,     0.31832,     0.31932,     0.32032,     0.32132,     0.32232,     0.32332,     0.32432,     0.32533,     0.32633,     0.32733,     0.32833,     0.32933,     0.33033,     0.33133,     0.33233,     0.33333,     0.33433,     0.33534,\n", "           0.33634,     0.33734,     0.33834,     0.33934,     0.34034,     0.34134,     0.34234,     0.34334,     0.34434,     0.34535,     0.34635,     0.34735,     0.34835,     0.34935,     0.35035,     0.35135,     0.35235,     0.35335,     0.35435,     0.35536,     0.35636,     0.35736,     0.35836,     0.35936,\n", "           0.36036,     0.36136,     0.36236,     0.36336,     0.36436,     0.36537,     0.36637,     0.36737,     0.36837,     0.36937,     0.37037,     0.37137,     0.37237,     0.37337,     0.37437,     0.37538,     0.37638,     0.37738,     0.37838,     0.37938,     0.38038,     0.38138,     0.38238,     0.38338,\n", "           0.38438,     0.38539,     0.38639,     0.38739,     0.38839,     0.38939,     0.39039,     0.39139,     0.39239,     0.39339,     0.39439,      0.3954,      0.3964,      0.3974,      0.3984,      0.3994,      0.4004,      0.4014,      0.4024,      0.4034,      0.4044,     0.40541,     0.40641,     0.40741,\n", "           0.40841,     0.40941,     0.41041,     0.41141,     0.41241,     0.41341,     0.41441,     0.41542,     0.41642,     0.41742,     0.41842,     0.41942,     0.42042,     0.42142,     0.42242,     0.42342,     0.42442,     0.42543,     0.42643,     0.42743,     0.42843,     0.42943,     0.43043,     0.43143,\n", "           0.43243,     0.43343,     0.43443,     0.43544,     0.43644,     0.43744,     0.43844,     0.43944,     0.44044,     0.44144,     0.44244,     0.44344,     0.44444,     0.44545,     0.44645,     0.44745,     0.44845,     0.44945,     0.45045,     0.45145,     0.45245,     0.45345,     0.45445,     0.45546,\n", "           0.45646,     0.45746,     0.45846,     0.45946,     0.46046,     0.46146,     0.46246,     0.46346,     0.46446,     0.46547,     0.46647,     0.46747,     0.46847,     0.46947,     0.47047,     0.47147,     0.47247,     0.47347,     0.47447,     0.47548,     0.47648,     0.47748,     0.47848,     0.47948,\n", "           0.48048,     0.48148,     0.48248,     0.48348,     0.48448,     0.48549,     0.48649,     0.48749,     0.48849,     0.48949,     0.49049,     0.49149,     0.49249,     0.49349,     0.49449,      0.4955,      0.4965,      0.4975,      0.4985,      0.4995,      0.5005,      0.5015,      0.5025,      0.5035,\n", "            0.5045,     0.50551,     0.50651,     0.50751,     0.50851,     0.50951,     0.51051,     0.51151,     0.51251,     0.51351,     0.51451,     0.51552,     0.51652,     0.51752,     0.51852,     0.51952,     0.52052,     0.52152,     0.52252,     0.52352,     0.52452,     0.52553,     0.52653,     0.52753,\n", "           0.52853,     0.52953,     0.53053,     0.53153,     0.53253,     0.53353,     0.53453,     0.53554,     0.53654,     0.53754,     0.53854,     0.53954,     0.54054,     0.54154,     0.54254,     0.54354,     0.54454,     0.54555,     0.54655,     0.54755,     0.54855,     0.54955,     0.55055,     0.55155,\n", "           0.55255,     0.55355,     0.55455,     0.55556,     0.55656,     0.55756,     0.55856,     0.55956,     0.56056,     0.56156,     0.56256,     0.56356,     0.56456,     0.56557,     0.56657,     0.56757,     0.56857,     0.56957,     0.57057,     0.57157,     0.57257,     0.57357,     0.57457,     0.57558,\n", "           0.57658,     0.57758,     0.57858,     0.57958,     0.58058,     0.58158,     0.58258,     0.58358,     0.58458,     0.58559,     0.58659,     0.58759,     0.58859,     0.58959,     0.59059,     0.59159,     0.59259,     0.59359,     0.59459,      0.5956,      0.5966,      0.5976,      0.5986,      0.5996,\n", "            0.6006,      0.6016,      0.6026,      0.6036,      0.6046,     0.60561,     0.60661,     0.60761,     0.60861,     0.60961,     0.61061,     0.61161,     0.61261,     0.61361,     0.61461,     0.61562,     0.61662,     0.61762,     0.61862,     0.61962,     0.62062,     0.62162,     0.62262,     0.62362,\n", "           0.62462,     0.62563,     0.62663,     0.62763,     0.62863,     0.62963,     0.63063,     0.63163,     0.63263,     0.63363,     0.63463,     0.63564,     0.63664,     0.63764,     0.63864,     0.63964,     0.64064,     0.64164,     0.64264,     0.64364,     0.64464,     0.64565,     0.64665,     0.64765,\n", "           0.64865,     0.64965,     0.65065,     0.65165,     0.65265,     0.65365,     0.65465,     0.65566,     0.65666,     0.65766,     0.65866,     0.65966,     0.66066,     0.66166,     0.66266,     0.66366,     0.66466,     0.66567,     0.66667,     0.66767,     0.66867,     0.66967,     0.67067,     0.67167,\n", "           0.67267,     0.67367,     0.67467,     0.67568,     0.67668,     0.67768,     0.67868,     0.67968,     0.68068,     0.68168,     0.68268,     0.68368,     0.68468,     0.68569,     0.68669,     0.68769,     0.68869,     0.68969,     0.69069,     0.69169,     0.69269,     0.69369,     0.69469,      0.6957,\n", "            0.6967,      0.6977,      0.6987,      0.6997,      0.7007,      0.7017,      0.7027,      0.7037,      0.7047,     0.70571,     0.70671,     0.70771,     0.70871,     0.70971,     0.71071,     0.71171,     0.71271,     0.71371,     0.71471,     0.71572,     0.71672,     0.71772,     0.71872,     0.71972,\n", "           0.72072,     0.72172,     0.72272,     0.72372,     0.72472,     0.72573,     0.72673,     0.72773,     0.72873,     0.72973,     0.73073,     0.73173,     0.73273,     0.73373,     0.73473,     0.73574,     0.73674,     0.73774,     0.73874,     0.73974,     0.74074,     0.74174,     0.74274,     0.74374,\n", "           0.74474,     0.74575,     0.74675,     0.74775,     0.74875,     0.74975,     0.75075,     0.75175,     0.75275,     0.75375,     0.75475,     0.75576,     0.75676,     0.75776,     0.75876,     0.75976,     0.76076,     0.76176,     0.76276,     0.76376,     0.76476,     0.76577,     0.76677,     0.76777,\n", "           0.76877,     0.76977,     0.77077,     0.77177,     0.77277,     0.77377,     0.77477,     0.77578,     0.77678,     0.77778,     0.77878,     0.77978,     0.78078,     0.78178,     0.78278,     0.78378,     0.78478,     0.78579,     0.78679,     0.78779,     0.78879,     0.78979,     0.79079,     0.79179,\n", "           0.79279,     0.79379,     0.79479,      0.7958,      0.7968,      0.7978,      0.7988,      0.7998,      0.8008,      0.8018,      0.8028,      0.8038,      0.8048,     0.80581,     0.80681,     0.80781,     0.80881,     0.80981,     0.81081,     0.81181,     0.81281,     0.81381,     0.81481,     0.81582,\n", "           0.81682,     0.81782,     0.81882,     0.81982,     0.82082,     0.82182,     0.82282,     0.82382,     0.82482,     0.82583,     0.82683,     0.82783,     0.82883,     0.82983,     0.83083,     0.83183,     0.83283,     0.83383,     0.83483,     0.83584,     0.83684,     0.83784,     0.83884,     0.83984,\n", "           0.84084,     0.84184,     0.84284,     0.84384,     0.84484,     0.84585,     0.84685,     0.84785,     0.84885,     0.84985,     0.85085,     0.85185,     0.85285,     0.85385,     0.85485,     0.85586,     0.85686,     0.85786,     0.85886,     0.85986,     0.86086,     0.86186,     0.86286,     0.86386,\n", "           0.86486,     0.86587,     0.86687,     0.86787,     0.86887,     0.86987,     0.87087,     0.87187,     0.87287,     0.87387,     0.87487,     0.87588,     0.87688,     0.87788,     0.87888,     0.87988,     0.88088,     0.88188,     0.88288,     0.88388,     0.88488,     0.88589,     0.88689,     0.88789,\n", "           0.88889,     0.88989,     0.89089,     0.89189,     0.89289,     0.89389,     0.89489,      0.8959,      0.8969,      0.8979,      0.8989,      0.8999,      0.9009,      0.9019,      0.9029,      0.9039,      0.9049,     0.90591,     0.90691,     0.90791,     0.90891,     0.90991,     0.91091,     0.91191,\n", "           0.91291,     0.91391,     0.91491,     0.91592,     0.91692,     0.91792,     0.91892,     0.91992,     0.92092,     0.92192,     0.92292,     0.92392,     0.92492,     0.92593,     0.92693,     0.92793,     0.92893,     0.92993,     0.93093,     0.93193,     0.93293,     0.93393,     0.93493,     0.93594,\n", "           0.93694,     0.93794,     0.93894,     0.93994,     0.94094,     0.94194,     0.94294,     0.94394,     0.94494,     0.94595,     0.94695,     0.94795,     0.94895,     0.94995,     0.95095,     0.95195,     0.95295,     0.95395,     0.95495,     0.95596,     0.95696,     0.95796,     0.95896,     0.95996,\n", "           0.96096,     0.96196,     0.96296,     0.96396,     0.96496,     0.96597,     0.96697,     0.96797,     0.96897,     0.96997,     0.97097,     0.97197,     0.97297,     0.97397,     0.97497,     0.97598,     0.97698,     0.97798,     0.97898,     0.97998,     0.98098,     0.98198,     0.98298,     0.98398,\n", "           0.98498,     0.98599,     0.98699,     0.98799,     0.98899,     0.98999,     0.99099,     0.99199,     0.99299,     0.99399,     0.99499,       0.996,       0.997,       0.998,       0.999,           1]), array([[    0.61511,     0.61511,     0.67605,     0.72957,     0.76685,     0.78244,     0.80383,     0.81986,      0.8318,     0.83916,     0.84213,     0.84833,     0.85492,     0.85839,     0.86618,     0.87295,     0.87587,     0.88177,     0.88497,     0.88629,     0.88731,     0.88833,     0.88934,\n", "            0.89071,     0.89287,     0.89479,      0.8952,     0.89561,     0.89602,     0.89643,     0.89683,     0.89724,     0.89765,     0.89806,     0.89847,     0.89887,     0.89928,     0.89977,     0.90034,      0.9009,     0.90146,     0.90202,     0.90259,     0.90315,     0.90371,     0.90443,\n", "             0.9092,     0.90984,     0.91047,      0.9111,     0.91174,     0.91237,     0.91301,     0.91364,     0.91501,     0.91723,     0.92078,     0.93478,     0.93649,      0.9382,     0.93946,     0.94005,     0.94064,     0.94122,     0.94181,      0.9424,     0.94299,     0.94358,     0.94417,\n", "            0.94464,       0.945,     0.94537,     0.94573,      0.9461,     0.94646,     0.94683,     0.94719,     0.94756,     0.94792,     0.94829,     0.94865,     0.94902,     0.94938,     0.94973,     0.94989,     0.95004,      0.9502,     0.95036,     0.95051,     0.95067,     0.95083,     0.95098,\n", "            0.95114,      0.9513,     0.95145,     0.95161,     0.95176,     0.95192,     0.95208,     0.95223,     0.95239,     0.95255,      0.9527,     0.95286,     0.95301,     0.95317,     0.95333,     0.95348,     0.95364,      0.9538,     0.95395,     0.95411,     0.95426,     0.95442,     0.95458,\n", "            0.95473,     0.95489,     0.95505,     0.95528,     0.95553,     0.95577,     0.95601,     0.95625,      0.9565,     0.95674,     0.95698,     0.95723,     0.95747,     0.95771,     0.95796,      0.9582,     0.95844,     0.95869,     0.95893,     0.95917,     0.95942,     0.95966,      0.9599,\n", "            0.96014,     0.96039,     0.96097,     0.96168,     0.96239,     0.96311,     0.96382,     0.96453,     0.96524,     0.96596,     0.96688,      0.9678,     0.96873,     0.96965,     0.97058,     0.97145,     0.97175,     0.97205,     0.97236,     0.97266,     0.97296,     0.97326,     0.97356,\n", "            0.97386,     0.97416,     0.97446,     0.97476,     0.97507,     0.97537,     0.97567,     0.97597,     0.97627,     0.97657,     0.97687,     0.97701,     0.97701,       0.977,       0.977,       0.977,     0.97699,     0.97699,     0.97699,     0.97698,     0.97698,     0.97698,     0.97697,\n", "            0.97697,     0.97697,     0.97696,     0.97696,     0.97696,     0.97695,     0.97695,     0.97695,     0.97694,     0.97694,     0.97694,     0.97693,     0.97693,     0.97693,     0.97692,     0.97692,     0.97692,     0.97691,     0.97691,     0.97691,      0.9769,      0.9769,      0.9769,\n", "            0.97689,     0.97689,     0.97689,     0.97688,     0.97688,     0.97694,     0.97726,     0.97759,     0.97791,     0.97824,     0.97856,     0.97889,     0.97922,     0.97954,     0.97987,     0.98019,     0.98052,     0.98084,     0.98117,      0.9815,     0.98182,     0.98215,     0.98247,\n", "            0.98259,     0.98263,     0.98267,     0.98272,     0.98276,      0.9828,     0.98284,     0.98289,     0.98293,     0.98297,     0.98301,     0.98306,      0.9831,     0.98314,     0.98318,     0.98323,     0.98327,     0.98331,     0.98335,      0.9834,     0.98344,     0.98348,     0.98353,\n", "            0.98357,     0.98361,     0.98365,      0.9837,     0.98374,     0.98378,     0.98382,     0.98387,     0.98391,     0.98395,     0.98399,     0.98404,     0.98408,     0.98412,     0.98416,     0.98421,     0.98425,     0.98429,     0.98433,     0.98438,     0.98442,     0.98446,      0.9845,\n", "            0.98455,     0.98459,     0.98463,     0.98467,     0.98472,     0.98476,      0.9848,     0.98484,     0.98489,     0.98493,     0.98497,     0.98501,     0.98506,      0.9851,     0.98514,     0.98518,     0.98523,     0.98527,     0.98531,     0.98535,      0.9854,     0.98544,     0.98548,\n", "            0.98552,     0.98557,     0.98561,     0.98565,     0.98569,     0.98574,     0.98578,     0.98582,     0.98586,     0.98591,     0.98595,     0.98599,     0.98603,     0.98608,     0.98612,     0.98616,      0.9862,     0.98625,     0.98629,     0.98633,     0.98637,     0.98642,     0.98646,\n", "             0.9865,     0.98654,     0.98659,     0.98663,     0.98667,     0.98671,     0.98676,      0.9868,     0.98684,     0.98688,     0.98693,     0.98697,     0.98701,     0.98705,      0.9871,     0.98714,     0.98718,     0.98722,     0.98727,     0.98731,     0.98735,     0.98739,     0.98744,\n", "            0.98748,     0.98752,     0.98756,     0.98761,     0.98765,     0.98769,     0.98774,     0.98778,     0.98782,     0.98786,     0.98791,     0.98795,     0.98799,     0.98803,     0.98808,     0.98812,     0.98816,      0.9882,     0.98825,     0.98829,     0.98832,     0.98834,     0.98837,\n", "            0.98839,     0.98841,     0.98844,     0.98846,     0.98848,     0.98851,     0.98853,     0.98855,     0.98858,      0.9886,     0.98862,     0.98865,     0.98867,     0.98869,     0.98872,     0.98874,     0.98876,     0.98878,     0.98881,     0.98883,     0.98885,     0.98888,      0.9889,\n", "            0.98892,     0.98895,     0.98897,     0.98899,     0.98902,     0.98904,     0.98906,     0.98909,     0.98911,     0.98913,     0.98916,     0.98918,      0.9892,     0.98923,     0.98925,     0.98927,      0.9893,     0.98932,     0.98934,     0.98937,     0.98939,     0.98941,     0.98944,\n", "            0.98946,     0.98948,     0.98951,     0.98953,     0.98955,     0.98958,      0.9896,     0.98962,     0.98965,     0.98967,     0.98969,     0.98972,     0.98974,     0.98976,     0.98979,     0.98981,     0.98983,     0.98986,     0.98988,      0.9899,     0.98993,     0.98995,     0.98997,\n", "               0.99,     0.99002,     0.99004,     0.99007,     0.99009,     0.99011,     0.99014,     0.99016,     0.99018,     0.99021,     0.99023,     0.99025,     0.99028,      0.9903,     0.99032,     0.99035,     0.99037,     0.99039,     0.99042,     0.99044,     0.99046,     0.99049,     0.99051,\n", "            0.99053,     0.99056,     0.99058,      0.9906,     0.99063,     0.99065,     0.99067,      0.9907,     0.99072,     0.99074,     0.99077,     0.99079,     0.99081,     0.99084,     0.99086,     0.99088,     0.99091,     0.99093,     0.99095,     0.99098,       0.991,     0.99102,     0.99105,\n", "            0.99107,     0.99109,     0.99112,     0.99114,     0.99116,     0.99119,     0.99121,     0.99123,     0.99126,     0.99128,      0.9913,     0.99133,     0.99135,     0.99137,      0.9914,     0.99142,     0.99144,     0.99147,     0.99149,     0.99151,     0.99154,     0.99156,     0.99158,\n", "            0.99161,     0.99163,     0.99165,     0.99168,      0.9917,     0.99172,     0.99175,     0.99177,     0.99179,     0.99182,     0.99184,     0.99186,     0.99189,     0.99191,     0.99193,     0.99196,     0.99198,       0.992,     0.99203,     0.99205,     0.99207,      0.9921,     0.99212,\n", "            0.99214,     0.99217,     0.99219,     0.99221,     0.99224,     0.99226,     0.99228,     0.99231,     0.99233,     0.99235,     0.99238,      0.9924,     0.99242,     0.99245,     0.99247,     0.99249,     0.99252,     0.99254,     0.99256,     0.99259,     0.99261,     0.99263,     0.99266,\n", "            0.99268,      0.9927,     0.99273,     0.99275,     0.99277,      0.9928,     0.99282,     0.99284,     0.99287,     0.99289,     0.99291,     0.99294,     0.99296,     0.99298,     0.99301,     0.99303,     0.99305,     0.99308,      0.9931,     0.99312,     0.99315,     0.99317,     0.99319,\n", "            0.99322,     0.99324,     0.99326,     0.99329,     0.99331,     0.99333,     0.99336,     0.99338,      0.9934,     0.99343,     0.99345,     0.99347,      0.9935,     0.99352,     0.99354,     0.99357,     0.99359,     0.99361,     0.99364,     0.99366,     0.99368,     0.99371,     0.99373,\n", "            0.99375,     0.99378,      0.9938,     0.99382,     0.99385,     0.99387,     0.99389,     0.99392,     0.99394,     0.99396,     0.99399,     0.99401,     0.99403,     0.99406,     0.99408,      0.9941,     0.99451,      0.9956,      0.9967,      0.9978,     0.99889,     0.99999,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1,\n", "                  1,           1,           1,           1,           1,           1,           1,           1,           1,           1,           1]]), 'Confidence', 'Precision'], [array([          0,    0.001001,    0.002002,    0.003003,    0.004004,    0.005005,    0.006006,    0.007007,    0.008008,    0.009009,     0.01001,    0.011011,    0.012012,    0.013013,    0.014014,    0.015015,    0.016016,    0.017017,    0.018018,    0.019019,     0.02002,    0.021021,    0.022022,    0.023023,\n", "          0.024024,    0.025025,    0.026026,    0.027027,    0.028028,    0.029029,     0.03003,    0.031031,    0.032032,    0.033033,    0.034034,    0.035035,    0.036036,    0.037037,    0.038038,    0.039039,     0.04004,    0.041041,    0.042042,    0.043043,    0.044044,    0.045045,    0.046046,    0.047047,\n", "          0.048048,    0.049049,     0.05005,    0.051051,    0.052052,    0.053053,    0.054054,    0.055055,    0.056056,    0.057057,    0.058058,    0.059059,     0.06006,    0.061061,    0.062062,    0.063063,    0.064064,    0.065065,    0.066066,    0.067067,    0.068068,    0.069069,     0.07007,    0.071071,\n", "          0.072072,    0.073073,    0.074074,    0.075075,    0.076076,    0.077077,    0.078078,    0.079079,     0.08008,    0.081081,    0.082082,    0.083083,    0.084084,    0.085085,    0.086086,    0.087087,    0.088088,    0.089089,     0.09009,    0.091091,    0.092092,    0.093093,    0.094094,    0.095095,\n", "          0.096096,    0.097097,    0.098098,    0.099099,      0.1001,      0.1011,      0.1021,      0.1031,      0.1041,     0.10511,     0.10611,     0.10711,     0.10811,     0.10911,     0.11011,     0.11111,     0.11211,     0.11311,     0.11411,     0.11512,     0.11612,     0.11712,     0.11812,     0.11912,\n", "           0.12012,     0.12112,     0.12212,     0.12312,     0.12412,     0.12513,     0.12613,     0.12713,     0.12813,     0.12913,     0.13013,     0.13113,     0.13213,     0.13313,     0.13413,     0.13514,     0.13614,     0.13714,     0.13814,     0.13914,     0.14014,     0.14114,     0.14214,     0.14314,\n", "           0.14414,     0.14515,     0.14615,     0.14715,     0.14815,     0.14915,     0.15015,     0.15115,     0.15215,     0.15315,     0.15415,     0.15516,     0.15616,     0.15716,     0.15816,     0.15916,     0.16016,     0.16116,     0.16216,     0.16316,     0.16416,     0.16517,     0.16617,     0.16717,\n", "           0.16817,     0.16917,     0.17017,     0.17117,     0.17217,     0.17317,     0.17417,     0.17518,     0.17618,     0.17718,     0.17818,     0.17918,     0.18018,     0.18118,     0.18218,     0.18318,     0.18418,     0.18519,     0.18619,     0.18719,     0.18819,     0.18919,     0.19019,     0.19119,\n", "           0.19219,     0.19319,     0.19419,      0.1952,      0.1962,      0.1972,      0.1982,      0.1992,      0.2002,      0.2012,      0.2022,      0.2032,      0.2042,     0.20521,     0.20621,     0.20721,     0.20821,     0.20921,     0.21021,     0.21121,     0.21221,     0.21321,     0.21421,     0.21522,\n", "           0.21622,     0.21722,     0.21822,     0.21922,     0.22022,     0.22122,     0.22222,     0.22322,     0.22422,     0.22523,     0.22623,     0.22723,     0.22823,     0.22923,     0.23023,     0.23123,     0.23223,     0.23323,     0.23423,     0.23524,     0.23624,     0.23724,     0.23824,     0.23924,\n", "           0.24024,     0.24124,     0.24224,     0.24324,     0.24424,     0.24525,     0.24625,     0.24725,     0.24825,     0.24925,     0.25025,     0.25125,     0.25225,     0.25325,     0.25425,     0.25526,     0.25626,     0.25726,     0.25826,     0.25926,     0.26026,     0.26126,     0.26226,     0.26326,\n", "           0.26426,     0.26527,     0.26627,     0.26727,     0.26827,     0.26927,     0.27027,     0.27127,     0.27227,     0.27327,     0.27427,     0.27528,     0.27628,     0.27728,     0.27828,     0.27928,     0.28028,     0.28128,     0.28228,     0.28328,     0.28428,     0.28529,     0.28629,     0.28729,\n", "           0.28829,     0.28929,     0.29029,     0.29129,     0.29229,     0.29329,     0.29429,      0.2953,      0.2963,      0.2973,      0.2983,      0.2993,      0.3003,      0.3013,      0.3023,      0.3033,      0.3043,     0.30531,     0.30631,     0.30731,     0.30831,     0.30931,     0.31031,     0.31131,\n", "           0.31231,     0.31331,     0.31431,     0.31532,     0.31632,     0.31732,     0.31832,     0.31932,     0.32032,     0.32132,     0.32232,     0.32332,     0.32432,     0.32533,     0.32633,     0.32733,     0.32833,     0.32933,     0.33033,     0.33133,     0.33233,     0.33333,     0.33433,     0.33534,\n", "           0.33634,     0.33734,     0.33834,     0.33934,     0.34034,     0.34134,     0.34234,     0.34334,     0.34434,     0.34535,     0.34635,     0.34735,     0.34835,     0.34935,     0.35035,     0.35135,     0.35235,     0.35335,     0.35435,     0.35536,     0.35636,     0.35736,     0.35836,     0.35936,\n", "           0.36036,     0.36136,     0.36236,     0.36336,     0.36436,     0.36537,     0.36637,     0.36737,     0.36837,     0.36937,     0.37037,     0.37137,     0.37237,     0.37337,     0.37437,     0.37538,     0.37638,     0.37738,     0.37838,     0.37938,     0.38038,     0.38138,     0.38238,     0.38338,\n", "           0.38438,     0.38539,     0.38639,     0.38739,     0.38839,     0.38939,     0.39039,     0.39139,     0.39239,     0.39339,     0.39439,      0.3954,      0.3964,      0.3974,      0.3984,      0.3994,      0.4004,      0.4014,      0.4024,      0.4034,      0.4044,     0.40541,     0.40641,     0.40741,\n", "           0.40841,     0.40941,     0.41041,     0.41141,     0.41241,     0.41341,     0.41441,     0.41542,     0.41642,     0.41742,     0.41842,     0.41942,     0.42042,     0.42142,     0.42242,     0.42342,     0.42442,     0.42543,     0.42643,     0.42743,     0.42843,     0.42943,     0.43043,     0.43143,\n", "           0.43243,     0.43343,     0.43443,     0.43544,     0.43644,     0.43744,     0.43844,     0.43944,     0.44044,     0.44144,     0.44244,     0.44344,     0.44444,     0.44545,     0.44645,     0.44745,     0.44845,     0.44945,     0.45045,     0.45145,     0.45245,     0.45345,     0.45445,     0.45546,\n", "           0.45646,     0.45746,     0.45846,     0.45946,     0.46046,     0.46146,     0.46246,     0.46346,     0.46446,     0.46547,     0.46647,     0.46747,     0.46847,     0.46947,     0.47047,     0.47147,     0.47247,     0.47347,     0.47447,     0.47548,     0.47648,     0.47748,     0.47848,     0.47948,\n", "           0.48048,     0.48148,     0.48248,     0.48348,     0.48448,     0.48549,     0.48649,     0.48749,     0.48849,     0.48949,     0.49049,     0.49149,     0.49249,     0.49349,     0.49449,      0.4955,      0.4965,      0.4975,      0.4985,      0.4995,      0.5005,      0.5015,      0.5025,      0.5035,\n", "            0.5045,     0.50551,     0.50651,     0.50751,     0.50851,     0.50951,     0.51051,     0.51151,     0.51251,     0.51351,     0.51451,     0.51552,     0.51652,     0.51752,     0.51852,     0.51952,     0.52052,     0.52152,     0.52252,     0.52352,     0.52452,     0.52553,     0.52653,     0.52753,\n", "           0.52853,     0.52953,     0.53053,     0.53153,     0.53253,     0.53353,     0.53453,     0.53554,     0.53654,     0.53754,     0.53854,     0.53954,     0.54054,     0.54154,     0.54254,     0.54354,     0.54454,     0.54555,     0.54655,     0.54755,     0.54855,     0.54955,     0.55055,     0.55155,\n", "           0.55255,     0.55355,     0.55455,     0.55556,     0.55656,     0.55756,     0.55856,     0.55956,     0.56056,     0.56156,     0.56256,     0.56356,     0.56456,     0.56557,     0.56657,     0.56757,     0.56857,     0.56957,     0.57057,     0.57157,     0.57257,     0.57357,     0.57457,     0.57558,\n", "           0.57658,     0.57758,     0.57858,     0.57958,     0.58058,     0.58158,     0.58258,     0.58358,     0.58458,     0.58559,     0.58659,     0.58759,     0.58859,     0.58959,     0.59059,     0.59159,     0.59259,     0.59359,     0.59459,      0.5956,      0.5966,      0.5976,      0.5986,      0.5996,\n", "            0.6006,      0.6016,      0.6026,      0.6036,      0.6046,     0.60561,     0.60661,     0.60761,     0.60861,     0.60961,     0.61061,     0.61161,     0.61261,     0.61361,     0.61461,     0.61562,     0.61662,     0.61762,     0.61862,     0.61962,     0.62062,     0.62162,     0.62262,     0.62362,\n", "           0.62462,     0.62563,     0.62663,     0.62763,     0.62863,     0.62963,     0.63063,     0.63163,     0.63263,     0.63363,     0.63463,     0.63564,     0.63664,     0.63764,     0.63864,     0.63964,     0.64064,     0.64164,     0.64264,     0.64364,     0.64464,     0.64565,     0.64665,     0.64765,\n", "           0.64865,     0.64965,     0.65065,     0.65165,     0.65265,     0.65365,     0.65465,     0.65566,     0.65666,     0.65766,     0.65866,     0.65966,     0.66066,     0.66166,     0.66266,     0.66366,     0.66466,     0.66567,     0.66667,     0.66767,     0.66867,     0.66967,     0.67067,     0.67167,\n", "           0.67267,     0.67367,     0.67467,     0.67568,     0.67668,     0.67768,     0.67868,     0.67968,     0.68068,     0.68168,     0.68268,     0.68368,     0.68468,     0.68569,     0.68669,     0.68769,     0.68869,     0.68969,     0.69069,     0.69169,     0.69269,     0.69369,     0.69469,      0.6957,\n", "            0.6967,      0.6977,      0.6987,      0.6997,      0.7007,      0.7017,      0.7027,      0.7037,      0.7047,     0.70571,     0.70671,     0.70771,     0.70871,     0.70971,     0.71071,     0.71171,     0.71271,     0.71371,     0.71471,     0.71572,     0.71672,     0.71772,     0.71872,     0.71972,\n", "           0.72072,     0.72172,     0.72272,     0.72372,     0.72472,     0.72573,     0.72673,     0.72773,     0.72873,     0.72973,     0.73073,     0.73173,     0.73273,     0.73373,     0.73473,     0.73574,     0.73674,     0.73774,     0.73874,     0.73974,     0.74074,     0.74174,     0.74274,     0.74374,\n", "           0.74474,     0.74575,     0.74675,     0.74775,     0.74875,     0.74975,     0.75075,     0.75175,     0.75275,     0.75375,     0.75475,     0.75576,     0.75676,     0.75776,     0.75876,     0.75976,     0.76076,     0.76176,     0.76276,     0.76376,     0.76476,     0.76577,     0.76677,     0.76777,\n", "           0.76877,     0.76977,     0.77077,     0.77177,     0.77277,     0.77377,     0.77477,     0.77578,     0.77678,     0.77778,     0.77878,     0.77978,     0.78078,     0.78178,     0.78278,     0.78378,     0.78478,     0.78579,     0.78679,     0.78779,     0.78879,     0.78979,     0.79079,     0.79179,\n", "           0.79279,     0.79379,     0.79479,      0.7958,      0.7968,      0.7978,      0.7988,      0.7998,      0.8008,      0.8018,      0.8028,      0.8038,      0.8048,     0.80581,     0.80681,     0.80781,     0.80881,     0.80981,     0.81081,     0.81181,     0.81281,     0.81381,     0.81481,     0.81582,\n", "           0.81682,     0.81782,     0.81882,     0.81982,     0.82082,     0.82182,     0.82282,     0.82382,     0.82482,     0.82583,     0.82683,     0.82783,     0.82883,     0.82983,     0.83083,     0.83183,     0.83283,     0.83383,     0.83483,     0.83584,     0.83684,     0.83784,     0.83884,     0.83984,\n", "           0.84084,     0.84184,     0.84284,     0.84384,     0.84484,     0.84585,     0.84685,     0.84785,     0.84885,     0.84985,     0.85085,     0.85185,     0.85285,     0.85385,     0.85485,     0.85586,     0.85686,     0.85786,     0.85886,     0.85986,     0.86086,     0.86186,     0.86286,     0.86386,\n", "           0.86486,     0.86587,     0.86687,     0.86787,     0.86887,     0.86987,     0.87087,     0.87187,     0.87287,     0.87387,     0.87487,     0.87588,     0.87688,     0.87788,     0.87888,     0.87988,     0.88088,     0.88188,     0.88288,     0.88388,     0.88488,     0.88589,     0.88689,     0.88789,\n", "           0.88889,     0.88989,     0.89089,     0.89189,     0.89289,     0.89389,     0.89489,      0.8959,      0.8969,      0.8979,      0.8989,      0.8999,      0.9009,      0.9019,      0.9029,      0.9039,      0.9049,     0.90591,     0.90691,     0.90791,     0.90891,     0.90991,     0.91091,     0.91191,\n", "           0.91291,     0.91391,     0.91491,     0.91592,     0.91692,     0.91792,     0.91892,     0.91992,     0.92092,     0.92192,     0.92292,     0.92392,     0.92492,     0.92593,     0.92693,     0.92793,     0.92893,     0.92993,     0.93093,     0.93193,     0.93293,     0.93393,     0.93493,     0.93594,\n", "           0.93694,     0.93794,     0.93894,     0.93994,     0.94094,     0.94194,     0.94294,     0.94394,     0.94494,     0.94595,     0.94695,     0.94795,     0.94895,     0.94995,     0.95095,     0.95195,     0.95295,     0.95395,     0.95495,     0.95596,     0.95696,     0.95796,     0.95896,     0.95996,\n", "           0.96096,     0.96196,     0.96296,     0.96396,     0.96496,     0.96597,     0.96697,     0.96797,     0.96897,     0.96997,     0.97097,     0.97197,     0.97297,     0.97397,     0.97497,     0.97598,     0.97698,     0.97798,     0.97898,     0.97998,     0.98098,     0.98198,     0.98298,     0.98398,\n", "           0.98498,     0.98599,     0.98699,     0.98799,     0.98899,     0.98999,     0.99099,     0.99199,     0.99299,     0.99399,     0.99499,       0.996,       0.997,       0.998,       0.999,           1]), array([[    0.98844,     0.98844,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,\n", "            0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,\n", "            0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,\n", "            0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,\n", "            0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,\n", "            0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,\n", "            0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,\n", "            0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98266,     0.98258,     0.98244,      0.9823,     0.98215,     0.98201,     0.98187,     0.98172,     0.98158,     0.98144,     0.98129,     0.98115,     0.98101,\n", "            0.98086,     0.98072,     0.98058,     0.98043,     0.98029,     0.98015,        0.98,     0.97986,     0.97972,     0.97957,     0.97943,     0.97929,     0.97914,       0.979,     0.97886,     0.97872,     0.97857,     0.97843,     0.97829,     0.97814,       0.978,     0.97786,     0.97771,\n", "            0.97757,     0.97743,     0.97728,     0.97714,       0.977,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,\n", "            0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,\n", "            0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,\n", "            0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,\n", "            0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,\n", "            0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,\n", "            0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,\n", "            0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,\n", "            0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,\n", "            0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,\n", "            0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,\n", "            0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,\n", "            0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,\n", "            0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,\n", "            0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,\n", "            0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,\n", "            0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,\n", "            0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97688,     0.97683,\n", "            0.97678,     0.97673,     0.97669,     0.97664,     0.97659,     0.97654,     0.97649,     0.97644,      0.9764,     0.97635,      0.9763,     0.97625,      0.9762,     0.97615,     0.97611,     0.97606,     0.97601,     0.97596,     0.97591,     0.97586,     0.97581,     0.97577,     0.97572,\n", "            0.97567,     0.97562,     0.97557,     0.97552,     0.97548,     0.97543,     0.97538,     0.97533,     0.97528,     0.97523,     0.97519,     0.97514,     0.97509,     0.97504,     0.97499,     0.97494,      0.9749,     0.97485,      0.9748,     0.97475,      0.9747,     0.97465,     0.97461,\n", "            0.97456,     0.97451,     0.97446,     0.97441,     0.97436,     0.97432,     0.97427,     0.97422,     0.97417,     0.97412,     0.97407,     0.97403,     0.97398,     0.97393,     0.97388,     0.97383,     0.97378,     0.97374,     0.97369,     0.97364,     0.97359,     0.97354,     0.97349,\n", "            0.97344,      0.9734,     0.97335,      0.9733,     0.97325,      0.9732,     0.97315,     0.97311,     0.97306,     0.97301,     0.97296,     0.97291,     0.97286,     0.97282,     0.97277,     0.97272,     0.97267,     0.97262,     0.97257,     0.97253,     0.97248,     0.97243,     0.97238,\n", "            0.97233,     0.97228,     0.97224,     0.97219,     0.97214,     0.97209,     0.97204,     0.97199,     0.97195,      0.9719,     0.97185,      0.9718,     0.97175,      0.9717,     0.97166,     0.97161,     0.97156,     0.97151,     0.97146,     0.97141,     0.97137,     0.97132,     0.97127,\n", "            0.97122,     0.97117,     0.97112,     0.97108,     0.97103,     0.97098,     0.97093,     0.97088,     0.97084,     0.97079,     0.97074,     0.97069,     0.97065,      0.9706,     0.97055,      0.9705,     0.97045,     0.97041,     0.97036,     0.97031,     0.97026,     0.97022,     0.97017,\n", "            0.97012,     0.97007,     0.97002,     0.96998,     0.96993,     0.96988,     0.96983,     0.96979,     0.96974,     0.96969,     0.96964,     0.96959,     0.96955,      0.9695,     0.96945,      0.9694,     0.96936,     0.96931,     0.96926,     0.96921,     0.96916,     0.96912,     0.96907,\n", "            0.96902,     0.96897,     0.96893,     0.96888,     0.96883,     0.96878,     0.96873,     0.96869,     0.96864,     0.96859,     0.96854,      0.9685,     0.96845,      0.9684,     0.96835,      0.9683,     0.96826,     0.96821,     0.96816,     0.96811,     0.96806,     0.96802,     0.96797,\n", "            0.96792,     0.96787,     0.96783,     0.96778,     0.96773,     0.96768,     0.96763,     0.96759,     0.96754,     0.96749,     0.96744,      0.9674,     0.96735,      0.9673,     0.96725,      0.9672,     0.96716,     0.96711,     0.96706,     0.96701,     0.96697,     0.96692,     0.96687,\n", "            0.96682,     0.96677,     0.96673,     0.96668,     0.96663,     0.96658,     0.96654,     0.96649,     0.96644,     0.96639,     0.96634,      0.9663,     0.96625,      0.9662,     0.96615,     0.96611,     0.96606,     0.96601,     0.96596,     0.96591,     0.96587,     0.96582,     0.96577,\n", "            0.96572,     0.96568,     0.96563,     0.96558,     0.96553,     0.96548,     0.96544,     0.96539,     0.96534,     0.96524,     0.96509,     0.96493,     0.96478,     0.96462,     0.96447,     0.96432,     0.96416,     0.96401,     0.96385,      0.9637,     0.96355,     0.96339,     0.96324,\n", "            0.96309,     0.96293,     0.96278,     0.96262,     0.96247,     0.96232,     0.96216,     0.96201,     0.96185,      0.9617,     0.96155,     0.96139,     0.96124,     0.96108,     0.96093,     0.96078,     0.96062,     0.96047,     0.96031,     0.96016,     0.96001,     0.95985,      0.9597,\n", "            0.95955,     0.95929,     0.95904,     0.95878,     0.95852,     0.95826,     0.95801,     0.95775,     0.95749,     0.95723,     0.95697,     0.95672,     0.95646,      0.9562,     0.95594,     0.95569,     0.95543,     0.95517,     0.95491,     0.95466,      0.9544,     0.95414,     0.95388,\n", "            0.95347,     0.95291,     0.95234,     0.95178,     0.95121,     0.95065,     0.95008,     0.94952,     0.94896,     0.94839,     0.94753,     0.94583,     0.94414,     0.94245,      0.9344,     0.93203,     0.92574,      0.9212,     0.91634,     0.91257,     0.91109,     0.90961,     0.90813,\n", "            0.90613,     0.90376,     0.90116,     0.89721,     0.89393,     0.89097,     0.87574,     0.86258,     0.85417,     0.85248,     0.85079,     0.84827,     0.84432,     0.83859,     0.83267,     0.82385,     0.81214,     0.80333,     0.79134,     0.75637,     0.74452,     0.73267,      0.6977,\n", "            0.66273,      0.6451,     0.62009,     0.58815,     0.55752,     0.53411,     0.45868,     0.42284,     0.37717,     0.33642,     0.27255,     0.24914,     0.22573,      0.1792,     0.13859,     0.11503,    0.074282,    0.039887,    0.027461,    0.013586,   0.0076608,           0,           0,\n", "                  0,           0,           0,           0,           0,           0,           0,           0,           0,           0,           0]]), 'Confidence', 'Recall']]\n", "fitness: 0.9834516490682695\n", "keys: ['metrics/precision(B)', 'metrics/recall(B)', 'metrics/mAP50(B)', 'metrics/mAP50-95(B)']\n", "maps: array([    0.98271])\n", "names: {0: 'Soup'}\n", "nt_per_class: array([173])\n", "nt_per_image: array([70])\n", "results_dict: {'metrics/precision(B)': 1.0, 'metrics/recall(B)': 0.9746541634237753, 'metrics/mAP50(B)': 0.9900907983957661, 'metrics/mAP50-95(B)': 0.9827139658096588, 'fitness': 0.9834516490682695}\n", "save_dir: PosixPath('runs/detect/train')\n", "speed: {'preprocess': 0.6087345342689644, 'inference': 34.70157241102006, 'loss': 0.0016539178344928212, 'postprocess': 3.0663818904268076}\n", "stats: {'tp': [], 'conf': [], 'pred_cls': [], 'target_cls': [], 'target_img': []}\n", "task: 'detect'"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["model = YOLO(\"yolo11m.pt\")\n", "data_yaml = \"/kaggle/working/yolo_params.yaml\"\n", "\n", "model.train(\n", "    data=data_yaml,\n", "    epochs=50,                \n", "    batch=4,                   \n", "    imgsz=1440,\n", "    patience=300,               \n", "    optimizer='SGD',\n", "    momentum=0.937,          \n", "    lr0=0.001,                \n", "    weight_decay=0.0005,       \n", "    cos_lr=True,               \n", "    save_period=2,             \n", "    workers=8,\n", "    # Augmentations\n", "    close_mosaic=20,\n", "    hsv_h=0.015,\n", "    hsv_s=0.7,\n", "    hsv_v=0.4,\n", "    flipud=0,\n", "    fliplr=0.5,\n", "    translate=0.1,\n", "    scale=0.5,\n", "    shear=0,\n", "    warmup_epochs= 3,\n", "    warmup_momentum= 1,\n", ")\n"]}, {"cell_type": "code", "execution_count": 7, "id": "0146cc17", "metadata": {"execution": {"iopub.execute_input": "2025-07-08T18:20:39.946393Z", "iopub.status.busy": "2025-07-08T18:20:39.946054Z", "iopub.status.idle": "2025-07-08T18:22:14.348266Z", "shell.execute_reply": "2025-07-08T18:22:14.347470Z"}, "papermill": {"duration": 95.859195, "end_time": "2025-07-08T18:22:15.120309", "exception": false, "start_time": "2025-07-08T18:20:39.261114", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[notice] ✅ Predictions saved: /kaggle/working/predictions/labels\n"]}], "source": ["model = YOLO(\"/kaggle/working/runs/detect/train/weights/best.pt\")\n", "\n", "test_images_path = \"/kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/TestImages/images\"\n", "output_dir = \"/kaggle/working/predictions/labels\"\n", "\n", "conf=0\n", "\n", "def predict(test_images_path, output_dir , model, conf):\n", "    os.makedirs(output_dir, exist_ok=True)\n", "    model.eval()\n", "    model.training = False\n", "    for img_path in Path(test_images_path).glob(\"*\"):\n", "        if img_path.suffix.lower() not in ['.png', '.jpg', '.jpeg']:\n", "            continue\n", "    \n", "        results = model.predict(img_path, conf=conf, augment=True, iou=0.5, max_det=600, verbose=False)  \n", "        \n", "        output_txt = Path(output_dir) / f\"{img_path.stem}.txt\"\n", "    \n", "        with open(output_txt, \"w\") as f:\n", "            for result in results:\n", "                img_height, img_width = result.orig_shape\n", "                for box in result.boxes.data:\n", "                    x1, y1, x2, y2, confidence, cls_id = box.tolist()\n", "    \n", "                    x_center = ((x1 + x2) / 2) / img_width\n", "                    y_center = ((y1 + y2) / 2) / img_height\n", "                    width = (x2 - x1) / img_width\n", "                    height = (y2 - y1) / img_height\n", "    \n", "                    f.write(f\"0 {confidence:.6f} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}\\n\")\n", "    \n", "    print(f\"[notice] ✅ Predictions saved: {output_dir}\")\n", "predict(test_images_path, output_dir , model, conf)"]}, {"cell_type": "code", "execution_count": 8, "id": "d33d6a00", "metadata": {"execution": {"iopub.execute_input": "2025-07-08T18:22:16.571753Z", "iopub.status.busy": "2025-07-08T18:22:16.571193Z", "iopub.status.idle": "2025-07-08T18:22:16.771624Z", "shell.execute_reply": "2025-07-08T18:22:16.770810Z"}, "papermill": {"duration": 0.971485, "end_time": "2025-07-08T18:22:16.772784", "exception": false, "start_time": "2025-07-08T18:22:15.801299", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   image_id                                  prediction_string\n", "0  IMG_9743  0 0.979370 0.541884 0.234399 0.118694 0.257391...\n", "1  IMG_9720  0 0.975430 0.327427 0.763585 0.194380 0.444147...\n", "2  IMG_9579  0 0.978461 0.245107 0.447259 0.095759 0.115216...\n", "3  IMG_9702  0 0.968594 0.408335 0.424705 0.087137 0.191796...\n", "4  IMG_9762  0 0.972057 0.275790 0.564792 0.110972 0.214248...\n", "5  IMG_9722  0 0.976965 0.326091 0.587241 0.109420 0.224405...\n", "6  IMG_9760  0 0.977822 0.552849 0.568105 0.101814 0.233653...\n", "7  IMG_9768  0 0.980758 0.203373 0.644006 0.129864 0.273661...\n", "8  IMG_9777  0 0.972121 0.201447 0.408286 0.125610 0.094396...\n", "9  IMG_9707  0 0.981342 0.720204 0.629532 0.164746 0.368053...\n", "[notice] ✅ Submission saved to /kaggle/working/submission.csv\n"]}], "source": ["# Convert predictions to CSV\n", "def predictions_to_csv(\n", "    preds_folder: str = \"/kaggle/working/predictions/labels\", \n", "    output_csv: str = \"/kaggle/working/submission.csv\", \n", "    test_images_folder: str = \"/kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/TestImages/images\",\n", "    allowed_extensions: tuple = (\".jpg\", \".png\", \".jpeg\")\n", "):\n", "    preds_path = Path(preds_folder)\n", "    test_images_path = Path(test_images_folder)\n", "\n", "    test_images = {p.stem for p in test_images_path.glob(\"*\") if p.suffix.lower() in allowed_extensions}\n", "\n", "    predictions = []\n", "    predicted_images = set()\n", "\n", "    for txt_file in preds_path.glob(\"*.txt\"):\n", "        image_id = txt_file.stem\n", "        predicted_images.add(image_id)\n", "\n", "        with open(txt_file, \"r\") as f:\n", "            valid_lines = [line.strip() for line in f if len(line.strip().split()) == 6]\n", "\n", "        pred_str = \" \".join(valid_lines) if valid_lines else \"no boxes\"\n", "        predictions.append({\"image_id\": image_id, \"prediction_string\": pred_str})\n", "\n", "    missing_images = test_images - predicted_images\n", "    for image_id in missing_images:\n", "        predictions.append({\"image_id\": image_id, \"prediction_string\": \"no boxes\"})\n", "\n", "    submission_df = pd.DataFrame(predictions)\n", "    submission_df.to_csv(output_csv, index=False, quoting=csv.QUOTE_MINIMAL)\n", "    print(submission_df.head(10))\n", "    print(f\"[notice] ✅ Submission saved to {output_csv}\")\n", "\n", "predictions_to_csv()"]}, {"cell_type": "code", "execution_count": 9, "id": "91888c6b", "metadata": {"execution": {"iopub.execute_input": "2025-07-08T18:22:18.255371Z", "iopub.status.busy": "2025-07-08T18:22:18.255108Z", "iopub.status.idle": "2025-07-08T18:22:18.267782Z", "shell.execute_reply": "2025-07-08T18:22:18.267216Z"}, "papermill": {"duration": 0.802569, "end_time": "2025-07-08T18:22:18.268957", "exception": false, "start_time": "2025-07-08T18:22:17.466388", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def filter_invalid_boxes(boxes, scores, labels):\n", "    filtered_boxes, filtered_scores, filtered_labels = [], [], []\n", "    for b, s, l in zip(boxes, scores, labels):\n", "        if abs(b[2] - b[0]) > 1e-6 and abs(b[3] - b[1]) > 1e-6:\n", "            filtered_boxes.append(b)\n", "            filtered_scores.append(s)\n", "            filtered_labels.append(l)\n", "    return filtered_boxes, filtered_scores, filtered_labels\n", "    \n", "def run_inference(models, image_sizes, test_images_path):\n", "    image_paths = [p for p in Path(test_images_path).glob(\"*\") if p.suffix.lower() in [\".jpg\", \".jpeg\", \".png\"]]\n", "    predictions = {}\n", "\n", "    for model_idx, model in enumerate(models):\n", "        model.eval()\n", "        predictions[model_idx] = {}\n", "        for size in image_sizes:\n", "            predictions[model_idx][size] = {}\n", "            pred = []\n", "            for img_path in image_paths:\n", "                image_id = img_path.stem\n", "                image = Image.open(img_path)\n", "                img_width, img_height = image.size\n", "\n", "                results = model.predict(source=str(img_path), conf=conf,iou=0.4, max_det=600, augment=True, imgsz=size, verbose=False)\n", "                boxes, scores, labels = [], [], []\n", "\n", "                for result in results:\n", "                    if result.boxes is None:\n", "                        continue\n", "                    boxes = result.boxes.xyxy.cpu().numpy().tolist()\n", "                    scores = result.boxes.conf.cpu().numpy().tolist()\n", "                    labels = result.boxes.cls.cpu().numpy().tolist()\n", "\n", "                    norm_boxes = [\n", "                        [x1 / img_width, y1 / img_height, x2 / img_width, y2 / img_height]\n", "                        for x1, y1, x2, y2 in boxes\n", "                    ]\n", "                    norm_boxes, scores, labels = filter_invalid_boxes(norm_boxes, scores, labels)\n", "\n", "                predictions[model_idx][size][image_id] = {\n", "                    \"boxes\": norm_boxes,\n", "                    \"scores\": scores,\n", "                    \"labels\": labels\n", "                }\n", "                \n", "                if boxes:\n", "                    prediction_string = \" \".join(\n", "                        f\"{int(lbl)} {score:.6f} {(b[0]+b[2])/2:.6f} {(b[1]+b[3])/2:.6f} {(b[2]-b[0]):.6f} {(b[3]-b[1]):.6f}\"\n", "                        for b, score, lbl in zip(norm_boxes, scores, labels)\n", "                    )\n", "                else:\n", "                    prediction_string = \"no boxes\"\n", "\n", "                pred.append({\n", "                    \"image_id\": image_id,\n", "                    \"prediction_string\": prediction_string\n", "                })\n", "\n", "            # Save CSV per model and size\n", "            df = pd.DataFrame(pred)\n", "            csv_path = f\"submission_{model_idx}_{size}.csv\"\n", "            df.to_csv(csv_path, index=False, quoting=csv.QUOTE_MINIMAL)\n", "            print(f\"[saved] {csv_path}\")\n", "            print(df.head(10))\n", "\n", "    return predictions\n", "\n", "def apply_wbf_and_save_final_submission(predictions, image_ids, output_path=\"submission_wbf.csv\"):\n", "    wbf_results = []\n", "\n", "    for image_id in image_ids:\n", "        all_boxes, all_scores, all_labels = [], [], []\n", "\n", "        for model_preds in predictions.values():\n", "            for size_preds in model_preds.values():\n", "                if image_id not in size_preds:\n", "                    continue\n", "                pred = size_preds[image_id]\n", "                if not pred[\"boxes\"]:\n", "                    continue\n", "                all_boxes.append(pred[\"boxes\"])\n", "                all_scores.append(pred[\"scores\"])\n", "                all_labels.append(pred[\"labels\"])\n", "\n", "        if not all_boxes:\n", "            pred_str = \"no boxes\"\n", "        else:\n", "            fused_boxes, fused_scores, fused_labels = weighted_boxes_fusion(\n", "                all_boxes, all_scores, all_labels, iou_thr=iou_thr, skip_box_thr=skip_box_thr\n", "            )\n", "\n", "            pred_str = \" \".join(\n", "                f\"{int(lbl)} {score:.6f} {(b[0]+b[2])/2:.6f} {(b[1]+b[3])/2:.6f} {(b[2]-b[0]):.6f} {(b[3]-b[1]):.6f}\"\n", "                for b, score, lbl in zip(fused_boxes, fused_scores, fused_labels)\n", "            )\n", "\n", "        wbf_results.append({\n", "            \"image_id\": image_id,\n", "            \"prediction_string\": pred_str\n", "        })\n", "\n", "    wbf_df = pd.DataFrame(wbf_results)\n", "    wbf_df.to_csv(output_path, index=False, quoting=csv.QUOTE_MINIMAL)\n", "    print(f\"[notice] ✅ WBF submission saved to {output_path}\")\n", "    print(wbf_df.head(10))\n", "\n"]}, {"cell_type": "code", "execution_count": 10, "id": "f60d79f0", "metadata": {"execution": {"iopub.execute_input": "2025-07-08T18:22:19.719664Z", "iopub.status.busy": "2025-07-08T18:22:19.719376Z", "iopub.status.idle": "2025-07-08T18:30:38.837507Z", "shell.execute_reply": "2025-07-08T18:30:38.836751Z"}, "papermill": {"duration": 500.55095, "end_time": "2025-07-08T18:30:39.509714", "exception": false, "start_time": "2025-07-08T18:22:18.958764", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[saved] submission_0_1056.csv\n", "   image_id                                  prediction_string\n", "0  IMG_9602  0 0.978105 0.751155 0.414007 0.138918 0.234465...\n", "1  IMG_9594  0 0.973315 0.377948 0.685115 0.099976 0.133865...\n", "2  IMG_9717  0 0.966220 0.675547 0.839970 0.102662 0.254904...\n", "3  IMG_9703  0 0.970252 0.350212 0.299336 0.075561 0.161691...\n", "4  IMG_9780  0 0.976510 0.525483 0.491825 0.202766 0.434468...\n", "5  IMG_9617  0 0.951072 0.350806 0.631029 0.140959 0.196154...\n", "6  IMG_9788  0 0.973612 0.337274 0.488361 0.080810 0.164181...\n", "7  IMG_9786  0 0.947800 0.513235 0.334796 0.047023 0.103401...\n", "8  IMG_9769  0 0.982107 0.309126 0.548453 0.321566 0.687003...\n", "9  IMG_9588  0 0.969230 0.883674 0.405312 0.109454 0.139244...\n", "[saved] submission_0_1440.csv\n", "   image_id                                  prediction_string\n", "0  IMG_9602  0 0.980345 0.750967 0.414286 0.138186 0.234941...\n", "1  IMG_9594  0 0.975847 0.377815 0.685476 0.100016 0.134148...\n", "2  IMG_9717  0 0.963971 0.360453 0.633848 0.057864 0.078129...\n", "3  IMG_9703  0 0.972480 0.350171 0.297797 0.074916 0.159806...\n", "4  IMG_9780  0 0.982855 0.524194 0.490008 0.201770 0.433390...\n", "5  IMG_9617  0 0.969010 0.349971 0.629748 0.141720 0.197662...\n", "6  IMG_9788  0 0.977011 0.337046 0.489379 0.080401 0.165971...\n", "7  IMG_9786  0 0.964123 0.351945 0.438792 0.058228 0.083653...\n", "8  IMG_9769  0 0.970212 0.309158 0.543360 0.316829 0.680302...\n", "9  IMG_9588  0 0.974216 0.884171 0.405296 0.109885 0.138941...\n", "[saved] submission_0_1920.csv\n", "   image_id                                  prediction_string\n", "0  IMG_9602  0 0.981178 0.603760 0.475023 0.170505 0.161966...\n", "1  IMG_9594  0 0.974175 0.377737 0.684638 0.100387 0.133986...\n", "2  IMG_9717  0 0.969740 0.676426 0.839979 0.103143 0.252745...\n", "3  IMG_9703  0 0.969984 0.350030 0.299650 0.075029 0.163572...\n", "4  IMG_9780  0 0.973930 0.524583 0.490048 0.200684 0.433183...\n", "5  IMG_9617  0 0.951528 0.349350 0.632169 0.139132 0.194479...\n", "6  IMG_9788  0 0.980515 0.637328 0.365289 0.088975 0.178072...\n", "7  IMG_9786  0 0.966425 0.352037 0.438931 0.058434 0.083300...\n", "8  IMG_9769  0 0.927607 0.309347 0.547067 0.307789 0.690877...\n", "9  IMG_9588  0 0.978316 0.884282 0.405739 0.109989 0.140562...\n", "[saved] submission_0_2560.csv\n", "   image_id                                  prediction_string\n", "0  IMG_9602  0 0.983062 0.603110 0.475030 0.169373 0.160961...\n", "1  IMG_9594  0 0.981860 0.378249 0.685608 0.100444 0.133936...\n", "2  IMG_9717  0 0.979387 0.360275 0.633146 0.056977 0.076734...\n", "3  IMG_9703  0 0.967881 0.350400 0.299978 0.075333 0.164697...\n", "4  IMG_9780  0 0.971777 0.273651 0.392393 0.120187 0.227660...\n", "5  IMG_9617  0 0.897043 0.561479 0.500306 0.074408 0.162872...\n", "6  IMG_9788  0 0.979361 0.337182 0.489296 0.080595 0.165307...\n", "7  IMG_9786  0 0.971324 0.351923 0.438804 0.058042 0.083837...\n", "8  IMG_9769  0 0.896907 0.313176 0.670805 0.296928 0.449614...\n", "9  IMG_9588  0 0.979842 0.883975 0.405683 0.109929 0.139889...\n", "[saved] submission_0_3200.csv\n", "   image_id                                  prediction_string\n", "0  IMG_9602  0 0.981136 0.603196 0.476272 0.169452 0.162644...\n", "1  IMG_9594  0 0.981753 0.378145 0.685586 0.100346 0.134215...\n", "2  IMG_9717  0 0.971802 0.360073 0.633505 0.056734 0.076838...\n", "3  IMG_9703  0 0.966380 0.350212 0.298090 0.075067 0.160187...\n", "4  IMG_9780  0 0.976085 0.273458 0.392621 0.120044 0.227576...\n", "5  IMG_9617  0 0.945684 0.561930 0.464162 0.074773 0.089588...\n", "6  IMG_9788  0 0.983712 0.337210 0.490014 0.081227 0.166059...\n", "7  IMG_9786  0 0.970368 0.351871 0.439299 0.057883 0.083374...\n", "8  IMG_9769  0 0.906562 0.312461 0.647888 0.298309 0.505157...\n", "9  IMG_9588  0 0.981285 0.884298 0.405828 0.109361 0.139626...\n", "[notice] ✅ WBF submission saved to submission_wbf.csv\n", "   image_id                                  prediction_string\n", "0  IMG_9602  0 0.979274 0.603247 0.475335 0.169435 0.162084...\n", "1  IMG_9594  0 0.977390 0.377979 0.685286 0.100234 0.134030...\n", "2  IMG_9717  0 0.967598 0.676072 0.838531 0.102852 0.250811...\n", "3  IMG_9703  0 0.969395 0.350205 0.298970 0.075181 0.161990...\n", "4  IMG_9780  0 0.973753 0.273603 0.392113 0.120187 0.227353...\n", "5  IMG_9617  0 0.821195 0.350114 0.631041 0.141001 0.196062...\n", "6  IMG_9788  0 0.978496 0.337216 0.489248 0.080767 0.165542...\n", "7  IMG_9786  0 0.963542 0.351921 0.438944 0.058317 0.083397...\n", "8  IMG_9769  0 0.936679 0.310597 0.589802 0.308636 0.605721...\n", "9  IMG_9588  0 0.976578 0.884081 0.405573 0.109724 0.139654...\n"]}], "source": ["import os\n", "from pathlib import Path\n", "import pandas as pd\n", "import csv\n", "from ultralytics import YOLO\n", "from ensemble_boxes import weighted_boxes_fusion\n", "from PIL import Image\n", "\n", "model_paths = [\n", "    \"/kaggle/working/runs/detect/train/weights/best.pt\",\n", "    # \"/kaggle/working/runs/detect/train/weights/last.pt\",\n", "]\n", "\n", "test_images_path = \"/kaggle/input/multi-instance-object-detection-challenge/Starter_Dataset/TestImages/images\"\n", "output_dir = \"/kaggle/working/predictions/labels\"\n", "conf = 0\n", "iou_thr = 0.5\n", "skip_box_thr = 0.01\n", "image_sizes = [1056, 1440, 1920, 2560, 3200]\n", "\n", "models = [YOLO(path) for path in model_paths]\n", "predictions = run_inference(models, image_sizes, test_images_path)\n", "\n", "image_ids = list(next(iter(next(iter(predictions.values())).values())).keys())\n", "\n", "apply_wbf_and_save_final_submission(predictions, image_ids)"]}, {"cell_type": "code", "execution_count": null, "id": "1e1df438", "metadata": {"papermill": {"duration": 0.680206, "end_time": "2025-07-08T18:30:40.953220", "exception": false, "start_time": "2025-07-08T18:30:40.273014", "status": "completed"}, "tags": []}, "outputs": [], "source": []}], "metadata": {"kaggle": {"accelerator": "gpu", "dataSources": [{"databundleVersionId": 12610790, "sourceId": 104781, "sourceType": "competition"}, {"datasetId": 7826989, "sourceId": 12410711, "sourceType": "datasetVersion"}], "dockerImageVersionId": 31041, "isGpuEnabled": true, "isInternetEnabled": true, "language": "python", "sourceType": "notebook"}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}, "papermill": {"default_parameters": {}, "duration": 8573.229783, "end_time": "2025-07-08T18:30:44.385537", "environment_variables": {}, "exception": null, "input_path": "__notebook__.ipynb", "output_path": "__notebook__.ipynb", "parameters": {}, "start_time": "2025-07-08T16:07:51.155754", "version": "2.6.0"}}, "nbformat": 4, "nbformat_minor": 5}