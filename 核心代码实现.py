#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多实例目标检测项目 - 核心代码实现
Multi-Instance Object Detection Challenge - Core Implementation

作者: [您的姓名]
日期: 2025-07-11
描述: 基于YOLO11的多实例目标检测解决方案，结合多尺度推理、TTA和WBF技术
"""

import os
import yaml
import pandas as pd
import numpy as np
from pathlib import Path
from PIL import Image
import torch
import csv

# 深度学习框架
from ultralytics import YOLO
from ensemble_boxes import weighted_boxes_fusion

# 设置随机种子确保结果可复现
np.random.seed(42)
torch.manual_seed(42)

class MultiInstanceDetector:
    """
    多实例目标检测器
    
    主要功能:
    1. 模型训练
    2. 多尺度推理
    3. 测试时增强(TTA)
    4. 加权边界框融合(WBF)
    """
    
    def __init__(self, model_path='yolo11m.pt'):
        """
        初始化检测器
        
        Args:
            model_path (str): 预训练模型路径
        """
        self.model = YOLO(model_path)
        self.image_sizes = [1056, 1440, 1920, 2560, 3200]  # 多尺度推理尺寸
        self.conf_threshold = 0.0      # 置信度阈值
        self.iou_threshold = 0.4       # NMS IoU阈值
        self.wbf_iou_thr = 0.5        # WBF IoU阈值
        self.skip_box_thr = 0.01      # WBF跳过阈值
        
    def prepare_data_config(self, data_paths, output_path='yolo_params.yaml'):
        """
        准备YOLO数据配置文件
        
        Args:
            data_paths (dict): 包含train、val、test路径的字典
            output_path (str): 输出配置文件路径
        """
        config = {
            'train': data_paths['train'],
            'val': data_paths['val'], 
            'test': data_paths['test'],
            'nc': 1,                    # 类别数量
            'names': ['Soup']           # 类别名称
        }
        
        with open(output_path, 'w') as f:
            yaml.dump(config, f, default_flow_style=False)
        
        print(f"✅ 数据配置文件已保存到: {output_path}")
        return output_path
    
    def train_model(self, data_config_path, epochs=50, imgsz=1440, batch=4):
        """
        训练YOLO模型
        
        Args:
            data_config_path (str): 数据配置文件路径
            epochs (int): 训练轮数
            imgsz (int): 图像尺寸
            batch (int): 批次大小
        
        Returns:
            训练结果
        """
        print("🚀 开始训练模型...")
        
        # 训练参数配置
        training_args = {
            'data': data_config_path,
            'epochs': epochs,
            'imgsz': imgsz,
            'batch': batch,
            'patience': 300,        # 早停耐心值
            'save_period': 2,       # 保存周期
            'device': 0,            # GPU设备
            'workers': 8,           # 数据加载线程数
            'amp': True,            # 混合精度训练
            'lr0': 0.001,          # 初始学习率
            'momentum': 0.937,      # SGD动量
            'weight_decay': 0.0005, # 权重衰减
            'warmup_epochs': 3,     # 预热轮数
            'box': 7.5,            # 边界框损失权重
            'cls': 0.5,            # 分类损失权重
            'dfl': 1.5,            # 分布焦点损失权重
            'hsv_h': 0.015,        # HSV色调增强
            'hsv_s': 0.7,          # HSV饱和度增强
            'hsv_v': 0.4,          # HSV明度增强
            'fliplr': 0.5,         # 水平翻转概率
            'mosaic': 1.0,         # Mosaic增强概率
        }
        
        # 开始训练
        results = self.model.train(**training_args)
        
        print("✅ 模型训练完成!")
        return results
    
    def filter_invalid_boxes(self, boxes, scores, labels):
        """
        过滤无效的边界框
        
        Args:
            boxes (list): 边界框列表 [x1, y1, x2, y2]
            scores (list): 置信度列表
            labels (list): 标签列表
        
        Returns:
            tuple: 过滤后的(boxes, scores, labels)
        """
        valid_indices = []
        
        for i, (box, score) in enumerate(zip(boxes, scores)):
            x1, y1, x2, y2 = box
            
            # 检查坐标有效性
            if not (0 <= x1 < x2 <= 1 and 0 <= y1 < y2 <= 1):
                continue
                
            # 检查最小尺寸
            width = x2 - x1
            height = y2 - y1
            if width < 0.01 or height < 0.01:
                continue
                
            # 检查置信度
            if score < self.skip_box_thr:
                continue
                
            valid_indices.append(i)
        
        return ([boxes[i] for i in valid_indices],
                [scores[i] for i in valid_indices], 
                [labels[i] for i in valid_indices])
    
    def predict_single_image(self, image_path, size):
        """
        对单张图像进行预测
        
        Args:
            image_path (str): 图像路径
            size (int): 推理图像尺寸
        
        Returns:
            dict: 预测结果 {'boxes': [], 'scores': [], 'labels': []}
        """
        # 加载图像获取原始尺寸
        image = Image.open(image_path)
        img_width, img_height = image.size
        
        # 模型预测 - 启用TTA
        results = self.model.predict(
            source=str(image_path),
            conf=self.conf_threshold,
            iou=self.iou_threshold,
            max_det=600,            # 最大检测数量
            augment=True,           # 启用测试时增强(TTA)
            imgsz=size,
            verbose=False
        )
        
        # 提取预测结果
        boxes, scores, labels = [], [], []
        
        for result in results:
            if result.boxes is None:
                continue
                
            # 获取边界框坐标和置信度
            boxes = result.boxes.xyxy.cpu().numpy().tolist()
            scores = result.boxes.conf.cpu().numpy().tolist()
            labels = result.boxes.cls.cpu().numpy().tolist()
            
            # 归一化边界框坐标到[0,1]范围
            norm_boxes = [
                [x1/img_width, y1/img_height, x2/img_width, y2/img_height]
                for x1, y1, x2, y2 in boxes
            ]
            
            # 过滤无效边界框
            norm_boxes, scores, labels = self.filter_invalid_boxes(
                norm_boxes, scores, labels
            )
            
            break  # 只处理第一个结果
        
        return {
            'boxes': norm_boxes,
            'scores': scores, 
            'labels': labels
        }
    
    def run_multiscale_inference(self, test_images_path):
        """
        运行多尺度推理
        
        Args:
            test_images_path (str): 测试图像目录路径
        
        Returns:
            dict: 所有尺度的预测结果
        """
        print("🔍 开始多尺度推理...")
        
        # 获取所有测试图像路径
        image_paths = list(Path(test_images_path).glob("*.jpg"))
        predictions = {}
        
        # 对每个尺度进行推理
        for size in self.image_sizes:
            print(f"  📏 处理尺寸: {size}")
            predictions[size] = {}
            
            # 对每张图像进行预测
            for img_path in image_paths:
                image_id = img_path.stem
                pred_result = self.predict_single_image(img_path, size)
                predictions[size][image_id] = pred_result
        
        print("✅ 多尺度推理完成!")
        return predictions
    
    def apply_weighted_boxes_fusion(self, predictions, output_path="submission_wbf.csv"):
        """
        应用加权边界框融合(WBF)算法
        
        Args:
            predictions (dict): 多尺度预测结果
            output_path (str): 输出CSV文件路径
        
        Returns:
            pandas.DataFrame: 融合后的预测结果
        """
        print("🔗 应用加权边界框融合(WBF)...")
        
        # 获取所有图像ID
        image_ids = list(next(iter(predictions.values())).keys())
        wbf_results = []
        
        for image_id in image_ids:
            all_boxes, all_scores, all_labels = [], [], []
            
            # 收集该图像在所有尺度下的预测结果
            for size_preds in predictions.values():
                if image_id not in size_preds:
                    continue
                    
                pred = size_preds[image_id]
                if not pred['boxes']:
                    continue
                    
                all_boxes.append(pred['boxes'])
                all_scores.append(pred['scores'])
                all_labels.append(pred['labels'])
            
            # 如果没有检测到任何目标
            if not all_boxes:
                pred_string = "no boxes"
            else:
                # 应用WBF算法融合多个预测结果
                fused_boxes, fused_scores, fused_labels = weighted_boxes_fusion(
                    all_boxes, all_scores, all_labels,
                    iou_thr=self.wbf_iou_thr,
                    skip_box_thr=self.skip_box_thr
                )
                
                # 格式化预测字符串 (类别 置信度 中心x 中心y 宽度 高度)
                pred_string = " ".join(
                    f"{int(lbl)} {score:.6f} {(b[0]+b[2])/2:.6f} "
                    f"{(b[1]+b[3])/2:.6f} {(b[2]-b[0]):.6f} {(b[3]-b[1]):.6f}"
                    for b, score, lbl in zip(fused_boxes, fused_scores, fused_labels)
                )
            
            wbf_results.append({
                'image_id': image_id,
                'prediction_string': pred_string
            })
        
        # 保存结果到CSV文件
        wbf_df = pd.DataFrame(wbf_results)
        wbf_df.to_csv(output_path, index=False, quoting=csv.QUOTE_MINIMAL)
        
        print(f"✅ WBF融合结果已保存到: {output_path}")
        print(f"📊 处理了 {len(wbf_results)} 张图像")
        
        return wbf_df

def main():
    """
    主函数 - 演示完整的检测流程
    """
    print("🎯 多实例目标检测项目启动")
    
    # 初始化检测器
    detector = MultiInstanceDetector('yolo11m.pt')
    
    # 数据路径配置 (需要根据实际情况修改)
    data_paths = {
        'train': [
            'data/clutter/train',
            'data/couch_far_10/train/images',
            'data/far_10_half_clutter/train/images',
            # ... 其他训练数据路径
        ],
        'val': [
            'data/clutter/val/images',
            'data/couch_far_10/val/images', 
            # ... 其他验证数据路径
        ],
        'test': 'data/TestImages'
    }
    
    # 1. 准备数据配置
    config_path = detector.prepare_data_config(data_paths)
    
    # 2. 训练模型 (如果需要)
    # detector.train_model(config_path, epochs=50)
    
    # 3. 加载训练好的模型
    detector.model = YOLO('best.pt')  # 使用训练好的权重
    
    # 4. 多尺度推理
    predictions = detector.run_multiscale_inference(data_paths['test'])
    
    # 5. 应用WBF融合
    final_results = detector.apply_weighted_boxes_fusion(predictions)
    
    print("🎉 检测流程完成!")
    print(f"📈 最终结果预览:")
    print(final_results.head())

if __name__ == "__main__":
    main()
